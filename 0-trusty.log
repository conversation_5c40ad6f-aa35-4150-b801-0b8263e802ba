meta  guid_buf_size = 37 
[    2.090942] c0 guid: 1.0 
[    2.090947] c0 in avb_slot_verify, l:1626. 
[    2.090949] c0 in avb_slot_verify, l:1640. 
[    2.090951] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.090953] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.090955] c0 slot_data[0] is 0xe04c5e08.
[    2.090957] c0 slot_data[1] is 0x0.
[    2.090958] c0 vboot_verify_ret is :0
[    2.090962] c0 enter copy just debug... 344.
[    2.090964] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20672 androidboot.vbmeta.digest=f45b1135c3f4b119a6ae042e4cc8506ab9d54359bc0e8d536c44
[    2.150231] c1 have got sip smc all from uboot###
[    2.150236] c1  args->params[0] is 0xa. ###
[    2.150343] c1 process_sip_call, case FUNCTYPE_GET_LCS res:0, tmp is 1
[    2.201534] c0 have got sip smc all from uboot###
[    2.201537] c0  args->params[0] is 0x36. ###
[    2.201540] c0 from uboot smc...addr:0x9f140000 len:0x400
[    2.201567] c0 xing offset = 25.
[    2.201570] c0 xing offset2 = 64.
[    2.201596] c0 xing offset = 25.
[    2.201598] c0 xing offset2 = 64.
[    2.205668] c0 have got sip smc all from uboot###
[    2.205670] c0  args->params[0] is 0x40. ###
[    2.205673] c0 from uboot smc(set chip uid)...addr:0x9f13f000 len:0x8
[    2.205677] c0 get cpu id: 0x743a41 0x60d90a8
[    2.205681] c0 chip uid from uboot(id=5) len=8:
[    2.205682] c0 41 3a 74 00 a8 90 0d 06 
[    2.205690] c0 key[5] data has been saved!
[    2.205722] c0 have got sip smc all from uboot###
[    2.205727] c0  args->params[0] is 0x22. ###
[    2.205732] c0 enter set rpmb 16777216
[    2.207592] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207610] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207666] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207680] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207783] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207793] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
<    2.207872> ss: sec_rpmb_bl_client_handle_msg: use new key
[    2.207895] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207906] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207917] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
<    2.207927> ss-ipc: 185: do_disconnect ev->handle ox3ea
<    2.207935> ss: sec_rpmb_bl_disconnect: handle 0x3ea
[    2.207949] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207965] c0 have got sip smc all from uboot###
[    2.207967] c0  args->params[0] is 0x25. ###
[    2.207969] c0 set rpmb type 205
[    2.208007] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208027] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208077] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208090] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208126] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208135] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208154] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208194] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208213] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208225] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208255] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208263] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208281] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208316] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208337] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208348] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208380] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208388] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208405] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208439] c0 have got sip smc all from uboot###
[    2.208441] c0  args->params[0] is 0x18. ###
[    2.208457] c0 process_sip_call, case FUNCTYPE_CHECK_CPU_FLASH_BIND, res is:1
[    2.771249] c3 entering scheduler on cpu 3
[    2.772524] c4 entering scheduler on cpu 4
[    2.773684] c5 entering scheduler on cpu 5
[    2.774745] c6 entering scheduler on cpu 6
[    2.775767] c7 entering scheduler on cpu 7
[    5.827576] c3 tam_load_request:1513: load look up com.android.trusty.gatekeeper
[    5.827588] c3 handle_conn_req:412: failed (-2) to send response
<    5.829748> ss: block_device_tipc_ns_init: ns device's block size 4040 block count 1048576
[    5.833326] c7 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    5.833355] c7 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    5.833386] c7 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    5.833389] c7 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    5.833392] c7 ta_manager_verify_img:447: RSA_hash
[    5.834950] c7 ta_manager_verify_img:506: RSA_verify
<    5.835401> block_cache_complete_read: load block 0 failed
<    5.835413> block_cache_load_entry: failed to load block 0
[    5.837903] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    5.837916] c0 trusty_app:(32) start 0xffffffffe0796000 size 0x00017000
[    5.837926] c0 trusty_app: whitelist.table 0x0, size: 0
[    5.837930] c0 trusty_app 11 uuid: 0x38ba0cdc 0xdf0e 0x11e4 0x9869 0x233fb6ae4795
[    5.837938] c0 trusty_app 0xffffffffe06c5e28: stack_sz=0x1000
[    5.837942] c0 trusty_app 0xffffffffe06c5e28: heap_sz=0x2000
[    5.837945] c0 trusty_app 0xffffffffe06c5e28: one_shot=0x0
[    5.837948] c0 trusty_app 0xffffffffe06c5e28: keep_alive=0x0
[    5.837951] c0 trusty_app 0xffffffffe06c5e28: flags=0x1c
[    5.837954] c0 ta_manager_write_ta:985: enter tam anti rollback
[    5.837962] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    5.837966] c0 ta_manager_write_ta:997: tam anti rollback ok
[    5.837969] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    5.837973] c0 trusty_tapp_init:
[    5.838057] c0 trusty_app 11: code: start 0x00008000 end 0x0001aa1c
[    5.838062] c0 trusty_app 11: data: start 0x0001b000 end 0x0001c000
[    5.838067] c0 trusty_app 11: bss:                end 0x0001b420
[    5.838071] c0 trusty_app 11: brk:  start 0x0001c000 end 0x0001e000
[    5.838075] c0 trusty_app 11: entry 0x0000b418
<    5.838094> trusty_gatekeeper_ta: 304: Initializing
<    5.838154> trusty_gatekeeper_ta: 89: ReseedRng
<    5.838525> trusty_gatekeeper_ta: 97: ReseedRng ok
[    5.838558] c0 tam_port_publish:1496: publish port com.android.trusty.gatekeeper
[    5.838590] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.gatekeeper accomplished!
<    5.839043> block_cache_complete_read: load block 1 failed
<    5.839057> block_cache_load_entry: failed to load block 1
<    5.839066> fs_init_from_super:641: clear requested, create empty, version 0
<    5.839690> ss: block_device_tipc_ns_init: create port com.android.trusty.storage.client.td success
<    5.854255> ss: cal_rpmb_block_size: rpmb write data-size 512 to addr (65530) sucessfull
<    5.854273> ss: cal_rpmb_block_size: set rpmb block size 512
<    5.860979> trusty_kernelbootcp: 75: cmd KERNEL_BOOTCP_UNLOCK_DDR
<    5.860994> trusty_kernelbootcp: 322: TA:kbc_unlock_ddr() 
[    5.861002] c1 enter SEC_KBC_GET_TEECFG_FLAG
[    5.861012] c1 pal:g_wifionly_flag = 0 
<    5.861015> trusty_kernelbootcp: 331: TA:g_ioctl_cunter = 1,g_wifionly_flag = 0
[    5.861023] c1 enter SEC_KBC_STOP_CP
[    5.861026] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861030] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861033] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861036] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861039] c1 dump_table() flag = 31 
[    5.861041] c1 dump_table() is_packed = 0 
[    5.861043] c1 kbc_stop_cp() enter MODEM_IMG
[    5.861045] c1 reg_addr = 0xffffffffe61dc174
[    5.861052] c1 before reg = 0400
[    5.861057] c1 after  reg = 0400
[    5.861060] c1 sleep 50ms start 
<    5.861124> ss: block_device_tipc_rpmb_init: rpmb device's block size 512 block count 32754
<    5.868132> fs_init_from_super:603: super block: files at 74 free at 75
<    5.875569> fs_init_from_super:634: loaded super block version 1
<    5.875607> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tp success
<    5.875621> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tdea success
<    5.875628> ss: block_device_tipc_rpmb_init: rb device's block size 4040 block count 1048576
<    5.881561> super_block_valid:344: bad magic, 0x2d64cd1b6a6974ae
<    5.884340> super_block_valid:344: bad magic, 0x2d64cd1b6a6974ae
<    5.884358> fs_init_from_super:641: clear requested, create empty, version 0
<    5.884404> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tr success
<    5.887127> sprdimgversion: sprdimgverion_create_port: rpmb read image/mode image version blk (65534,65532) successful.
<    5.887165> ss: rpmb_proxy_connect: create port com.spreadtrum.sprdimgversion success
<    5.888753> sec_rpmbdata: sec_rpmbdata_create_port: rpmb read sec rpmb data (13, 1) (65517, 65529) successful.
<    5.888792> ss: rpmb_proxy_connect: create port com.spreadtrum.secrpmbdata success
[    5.911576] c1 reg_addr = 0xffffffffe61ccb98
[    5.911590] c1 before reg = 4800
[    5.911595] c1 after  reg = 4800
[    5.911597] c1 sleep 50ms start 
[    5.962358] c1 reg_addr = 0xffffffffe61cc818
[    5.962371] c1 before reg = 0006
[    5.962377] c1 after  reg = 0006
[    5.962380] c1 sleep 50ms start 
[    6.012455] c1 reg_addr = 0xffffffffe61cc330
[    6.012470] c1 before reg = 2010101
[    6.012476] c1 after  reg = 2010101
[    6.012479] c1 sleep 50ms start 
[    6.062549] c1 reg_addr = 0xffffffffe61dc08c
[    6.062562] c1 before reg = 0001
[    6.062568] c1 after  reg = 0001
[    6.062571] c1 sleep 50ms start SP_IMG
[    6.112749] c1 sleep end 
[    6.113106] c1 kbc_stop_cp() leave 
[    6.113116] c1 enter SEC_KBC_GET_LOAD_MODEM_FLAG
[    6.113119] c1 pal: g_load_modem_flag = 0 
<    6.113124> trusty_kernelbootcp: 339: TA:g_ioctl_cunter = 2,g_load_modem_flag = 0
<    6.113138> trusty_kernelbootcp: 340: TA:SEC_KBC_STOP_CP() ret = 0
<    6.113145> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113153> footer
<    6.113157> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113165> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113172> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113180> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113188> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113194> footer
<    6.113198> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113205> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113213> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113220> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113228> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113234> footer
<    6.113237> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113245> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113252> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113260> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113267> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113273> footer
<    6.113277> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113284> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113292> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113299> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113307> trusty_kernelbootcp: 287: dump_table() flag = 31 
<    6.113313> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
<    6.113379> trusty_kernelbootcp: 348: TA:SEC_FIREWALL_UNLOCK_CP_DDR() ret = 0
<    6.113393> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
[    6.332363] c2 tam_load_request:1513: load look up com.android.trusty.identity
[    6.332376] c2 handle_conn_req:412: failed (-2) to send response
[    6.343646] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    6.343682] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.347052] c3 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    6.347066] c3 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.347070] c3 ta_manager_verify_img:447: RSA_hash
[    6.353103] c0 ta_manager_verify_img:506: RSA_verify
[    6.356245] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    6.356258] c0 trusty_app:(32) start 0xffffffffe07cf000 size 0x0008b000
[    6.356269] c0 trusty_app: whitelist.table 0x0, size: 0
[    6.356310] c0 trusty_app 12 uuid: 0x3f3010ec 0xfc8 0xc8a2 0x9110 0xc5ef1de1233a
[    6.356319] c0 trusty_app 0xffffffffe06bee48: stack_sz=0x40000
[    6.356323] c0 trusty_app 0xffffffffe06bee48: heap_sz=0x40000
[    6.356326] c0 trusty_app 0xffffffffe06bee48: one_shot=0x0
[    6.356330] c0 trusty_app 0xffffffffe06bee48: keep_alive=0x0
[    6.356333] c0 trusty_app 0xffffffffe06bee48: flags=0x1c
[    6.356336] c0 ta_manager_write_ta:985: enter tam anti rollback
[    6.356344] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    6.356349] c0 ta_manager_write_ta:997: tam anti rollback ok
[    6.356352] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    6.356356] c0 trusty_tapp_init:
[    6.356497] c0 trusty_app 12: code: start 0x00008000 end 0x0008e3c4
[    6.356502] c0 trusty_app 12: data: start 0x0008f000 end 0x00090000
[    6.356506] c0 trusty_app 12: bss:                end 0x0008fa14
[    6.356510] c0 trusty_app 12: brk:  start 0x00090000 end 0x000d0000
[    6.356514] c0 trusty_app 12: entry 0x00011130
[    6.356553] c0 tam_port_publish:1501:  other port com.android.trusty.identity.secure
[    6.356570] c0 tam_port_publish:1496: publish port com.android.trusty.identity
[    6.356688] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.identity accomplished!
<    6.623423> ss: block_device_tipc_prodnv_init: prodnv device's block size 4040 block count 1048576
<    6.659762> fs_init_from_super:603: super block: files at 0 free at 2
<    6.659795> fs_init_from_super:605: probe super block's files block [0] failed, fs is corrupted.
<    6.660270> fs_init_from_super:634: loaded super block version 2
<    6.660309> ss: block_device_tipc_prodnv_init: create port com.android.trusty.storage.client.tn success
[    6.955852] c5 tam_load_request:1513: load look up com.android.trusty.faceid
[    6.955864] c5 handle_conn_req:412: failed (-2) to send response
[    6.963676] c0 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    6.963713] c0 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.963743] c0 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    6.963747] c0 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.963751] c0 ta_manager_verify_img:447: RSA_hash
[    6.964345] c0 ta_manager_verify_img:506: RSA_verify
[    6.965909] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    6.965920] c0 trusty_app:(32) start 0xffffffffe08ee000 size 0x00015000
[    6.965931] c0 trusty_app: whitelist.table 0x0, size: 0
[    6.965935] c0 trusty_app 13 uuid: 0xf4bc36e6 0x8ec2 0x46e2 0xa82e 0xf7cb6cdc6f72
[    6.965944] c0 trusty_app 0xffffffffe0723de0: stack_sz=0x2000
[    6.965947] c0 trusty_app 0xffffffffe0723de0: heap_sz=0x6000
[    6.965950] c0 trusty_app 0xffffffffe0723de0: one_shot=0x0
[    6.965953] c0 trusty_app 0xffffffffe0723de0: keep_alive=0x0
[    6.965957] c0 trusty_app 0xffffffffe0723de0: flags=0x1c
[    6.965959] c0 ta_manager_write_ta:985: enter tam anti rollback
[    6.965967] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    6.965971] c0 ta_manager_write_ta:997: tam anti rollback ok
[    6.965975] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    6.965980] c0 trusty_tapp_init:
[    6.966056] c0 trusty_app 13: code: start 0x00008000 end 0x000189cc
[    6.966061] c0 trusty_app 13: data: start 0x00019000 end 0x0001a000
[    6.966067] c0 trusty_app 13: bss:                end 0x0001941c
[    6.966070] c0 trusty_app 13: brk:  start 0x0001a000 end 0x00020000
[    6.966075] c0 trusty_app 13: entry 0x0000b430
[    6.966113] c0 tam_port_publish:1496: publish port com.android.trusty.faceid
[    6.966127] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.faceid accomplished!
<    7.180371> trusty_kernelbootcp: 67: cmd KERNEL_BOOTCP_VERIFY_ALL
<    7.180386> trusty_kernelbootcp: 177: TA:kbc_verify_all_avb2() 
<    7.180392> trusty_kernelbootcp: 281: dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
<    7.180401> footer
<    7.180405> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180413> 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
<    7.180420> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180428> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180436> trusty_kernelbootcp: 281: dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
<    7.180443> footer
<    7.180446> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180454> 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
<    7.180461> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180469> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180476> trusty_kernelbootcp: 281: dump_table() len = 440000 maplen = 440000 addr = 89620000 
<    7.180483> footer
<    7.180487> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180494> 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
<    7.180501> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180509> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180516> trusty_kernelbootcp: 281: dump_table() len = 200000 maplen = 200000 addr = 88040000 
<    7.180523> footer
<    7.180527> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180534> 0010 00 0b a0 08 00 00 00 00 00 0b b0 00 00 00 00 00
<    7.180542> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180549> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180557> trusty_kernelbootcp: 287: dump_table() flag = 31 
<    7.180562> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
[    7.180572] c3 enter sec_kbc_check_verify_table
<    7.180591> trusty_kernelbootcp: 167: TA:table->flag = 0x1f:
<    7.180660> trusty_kernelbootcp: 182: TA:SEC_FIREWALL_LOCK_CP_DRR() ret = 0
[    7.180667] c3 enter SEC_KBC_VERIFY_ALL_V2
[    7.180670] c3 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[    7.180675] c3 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[    7.180678] c3 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[    7.180682] c3 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[    7.180685] c3 dump_table() flag = 31 
[    7.180688] c3 dump_table() is_packed = 0 
[    7.180691] c3 [kbc]kbc_image_verify_v2() enter.
[    7.180695] c3 [kbc]call mem map:ns addr = 0x8b000000 map len = 0x1380000 
[    7.181027] c3 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.181031] c3 pAddr:
[    7.181033] c3 0000 4d 45 43 50 56 31 2e 30 00 06 00 00 00 00 00 00
[    7.181048] c3 [kbc]avb_userdata_set() packed offset = 0x0
[    7.181051] c3 [kbc]start verify... 
[    7.181052] c3 [kbc]avb_check_kbc_image() name = l_modem
[    7.181058] c3 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_modem
[    7.181062] c3 ### enter avb_slot_verify. ###
[    7.181071] c3 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.181096] c3 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.183880] c3 partition: vbmeta vbmeta_verify_ret is :0. 
[    7.183885] c3 Enter: implement validate_vbmeta_public_key().
[    7.183887] c3 dump public_key_hash: public_key_hash_length:32. 
[    7.183890] c3 cal_sha256(): enter cal_sha256 
[    7.183899] c3 expected_public_key is matched.
[    7.183902] c3 enter implement read_rollback_index().
[    7.183904] c3 read_is_device_unlocked() rollback_index_slot = 0 
[    7.183906] c3 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.183910] c3 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.183916] c3 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.183920] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183927] c3 Skip verify chain_partition[boot] while load verify l_modem partition... 
[    7.183931] c3 n = 0
[    7.183932] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183936] c3 Skip verify chain_partition[dtbo] while load verify l_modem partition... 
[    7.183939] c3 n = 1
[    7.183941] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183944] c3 Skip verify chain_partition[init_boot] while load verify l_modem partition... 
[    7.183947] c3 n = 2
[    7.183949] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183952] c3 Skip verify chain_partition[vbmeta_odm] while load verify l_modem partition... 
[    7.183956] c3 n = 3
[    7.183957] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183961] c3 Skip verify chain_partition[vbmeta_product] while load verify l_modem partition... 
[    7.183964] c3 n = 4
[    7.183966] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183969] c3 Skip verify chain_partition[vbmeta_system] while load verify l_modem partition... 
[    7.183972] c3 n = 5
[    7.183974] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183978] c3 Skip verify chain_partition[vbmeta_system_ext] while load verify l_modem partition... 
[    7.183981] c3 n = 6
[    7.183982] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183986] c3 Skip verify chain_partition[vbmeta_vendor] while load verify l_modem partition... 
[    7.183989] c3 n = 7
[    7.183991] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183994] c3 Skip verify chain_partition[vendor_boot] while load verify l_modem partition... 
[    7.183997] c3 n = 8
[    7.183999] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.184002] c3 chain partition founded: n = 9
[    7.184101] c3 [kbc]read partition name: l_modem, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.184109] c3 [kbc]read footer
[    7.184111] c3 dump footer
[    7.184113] c3 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.184128] c3 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
[    7.184142] c3 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.184157] c3 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.184172] c3 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.184178] c3 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_modem, offset = 0xd39000, num_bytes = 0x840
[    7.184182] c3 [kbc]read certificate: img_addr = 0xe662e000
[    7.184185] c3 [kbc]read certificate: offset = 0xd39000
[    7.184189] c3 dump certificate
[    7.184191] c3 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.184206] c3 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.186627] c3 partition: l_modem vbmeta_verify_ret is :0. 
[    7.186636] c3 enter implement read_rollback_index().
[    7.186638] c3 read_is_device_unlocked() rollback_index_slot = 11 
[    7.186641] c3 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.186645] c3 Info: g_sprd_vboot_version.img_ver[11]= 0
[    7.186649] c3 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.186653] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.186659] c3 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.186661] c3 check dat cp
[    7.307371] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.307382] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.307387] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307392] c0 Skip verify chain_partition[l_ldsp] while load verify l_modem partition... 
[    7.307395] c0 n = 10
[    7.307397] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307401] c0 Skip verify chain_partition[l_gdsp] while load verify l_modem partition... 
[    7.307404] c0 n = 11
[    7.307406] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307410] c0 Skip verify chain_partition[pm_sys] while load verify l_modem partition... 
[    7.307413] c0 n = 12
[    7.307416] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307420] c0 Skip verify chain_partition[l_agdsp] while load verify l_modem partition... 
[    7.307423] c0 n = 13
[    7.307425] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307428] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307432] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307435] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307439] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307442] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307447] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.307451] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.307462] c0 read_is_device_unlocked() ret = 0 
[    7.309408] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.309416] c0 partition: vbmeta  guid_buf_size = 37 
[    7.309419] c0 guid: 1.0 
[    7.309426] c0 in avb_slot_verify, l:1626. 
[    7.309430] c0 in avb_slot_verify, l:1640. 
[    7.309433] c0 [kbc]avb_slot_verify result is 0.
[    7.309435] c0 [kbc]l_modem_avb_slot_verify result is OK.
[    7.309439] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.309441] c0 [kbc]slot_data[1] is 0x0.
[    7.309448] c0 [kbc]ret = 0
[    7.309640] c0 [kbc]call mem map:ns addr = 0x89aa8000 map len = 0xb00000 
[    7.309834] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.309838] c0 pAddr:
[    7.309840] c0 0000 00 5a 5a 5a 08 73 e6 d7 b0 20 00 18 08 db e6 d7
[    7.309855] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.309859] c0 [kbc]start verify... 
[    7.309861] c0 [kbc]avb_check_kbc_image() name = l_ldsp
[    7.309866] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_ldsp
[    7.309869] c0 ### enter avb_slot_verify. ###
[    7.309876] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.309901] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.309905] c0 enter implement read_rollback_index().
[    7.309907] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.309910] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.309913] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.309919] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.309923] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309965] c0 Skip verify chain_partition[boot] while load verify l_ldsp partition... 
[    7.309969] c0 n = 0
[    7.309971] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309974] c0 Skip verify chain_partition[dtbo] while load verify l_ldsp partition... 
[    7.309977] c0 n = 1
[    7.309979] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309983] c0 Skip verify chain_partition[init_boot] while load verify l_ldsp partition... 
[    7.309986] c0 n = 2
[    7.309987] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309991] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_ldsp partition... 
[    7.309994] c0 n = 3
[    7.309996] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309999] c0 Skip verify chain_partition[vbmeta_product] while load verify l_ldsp partition... 
[    7.310002] c0 n = 4
[    7.310004] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310008] c0 Skip verify chain_partition[vbmeta_system] while load verify l_ldsp partition... 
[    7.310011] c0 n = 5
[    7.310013] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310016] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_ldsp partition... 
[    7.310019] c0 n = 6
[    7.310021] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310025] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_ldsp partition... 
[    7.310028] c0 n = 7
[    7.310029] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310033] c0 Skip verify chain_partition[vendor_boot] while load verify l_ldsp partition... 
[    7.310036] c0 n = 8
[    7.310038] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310041] c0 Skip verify chain_partition[l_modem] while load verify l_ldsp partition... 
[    7.310045] c0 n = 9
[    7.310046] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310050] c0 chain partition founded: n = 10
[    7.310054] c0 [kbc]read partition name: l_ldsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.310058] c0 [kbc]read footer
[    7.310060] c0 dump footer
[    7.310062] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.310119] c0 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
[    7.310134] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.310148] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.310163] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.310169] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_ldsp, offset = 0x300000, num_bytes = 0x840
[    7.310174] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.310177] c0 [kbc]read certificate: offset = 0x300000
[    7.310181] c0 dump certificate
[    7.310183] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.310198] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.314329] c0 partition: l_ldsp vbmeta_verify_ret is :0. 
[    7.314346] c0 enter implement read_rollback_index().
[    7.314348] c0 read_is_device_unlocked() rollback_index_slot = 12 
[    7.314351] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.314355] c0 Info: g_sprd_vboot_version.img_ver[12]= 0
[    7.314362] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.314366] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.314374] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.314377] c0 check dat cp
[    7.350245] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.350255] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.350260] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350266] c0 Skip verify chain_partition[l_gdsp] while load verify l_ldsp partition... 
[    7.350269] c0 n = 11
[    7.350271] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350275] c0 Skip verify chain_partition[pm_sys] while load verify l_ldsp partition... 
[    7.350278] c0 n = 12
[    7.350280] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350284] c0 Skip verify chain_partition[l_agdsp] while load verify l_ldsp partition... 
[    7.350287] c0 n = 13
[    7.350289] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350293] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350296] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350300] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350303] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350306] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350311] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.350317] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.350327] c0 read_is_device_unlocked() ret = 0 
[    7.350774] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.350779] c0 partition: vbmeta  guid_buf_size = 37 
[    7.350783] c0 guid: 1.0 
[    7.350789] c0 in avb_slot_verify, l:1626. 
[    7.350792] c0 in avb_slot_verify, l:1640. 
[    7.350796] c0 [kbc]avb_slot_verify result is 0.
[    7.350798] c0 [kbc]l_ldsp_avb_slot_verify result is OK.
[    7.350801] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.350804] c0 [kbc]slot_data[1] is 0x0.
[    7.350810] c0 [kbc]ret = 0
[    7.350905] c0 [kbc]call mem map:ns addr = 0x89620000 map len = 0x440000 
[    7.350988] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.350991] c0 pAddr:
[    7.350993] c0 0000 2e 50 53 44 38 38 43 53 5f 53 30 30 00 4d 53 47
[    7.351008] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.351010] c0 [kbc]start verify... 
[    7.351012] c0 [kbc]avb_check_kbc_image() name = l_gdsp
[    7.351016] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_gdsp
[    7.351020] c0 ### enter avb_slot_verify. ###
[    7.351027] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.351052] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.351056] c0 enter implement read_rollback_index().
[    7.351057] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.351060] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.351064] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.351070] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.351074] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351078] c0 Skip verify chain_partition[boot] while load verify l_gdsp partition... 
[    7.351081] c0 n = 0
[    7.351083] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351087] c0 Skip verify chain_partition[dtbo] while load verify l_gdsp partition... 
[    7.351090] c0 n = 1
[    7.351091] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351095] c0 Skip verify chain_partition[init_boot] while load verify l_gdsp partition... 
[    7.351098] c0 n = 2
[    7.351100] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351103] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_gdsp partition... 
[    7.351106] c0 n = 3
[    7.351108] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351111] c0 Skip verify chain_partition[vbmeta_product] while load verify l_gdsp partition... 
[    7.351115] c0 n = 4
[    7.351116] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351120] c0 Skip verify chain_partition[vbmeta_system] while load verify l_gdsp partition... 
[    7.351123] c0 n = 5
[    7.351124] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351128] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_gdsp partition... 
[    7.351131] c0 n = 6
[    7.351133] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351136] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_gdsp partition... 
[    7.351139] c0 n = 7
[    7.351141] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351145] c0 Skip verify chain_partition[vendor_boot] while load verify l_gdsp partition... 
[    7.351148] c0 n = 8
[    7.351149] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351153] c0 Skip verify chain_partition[l_modem] while load verify l_gdsp partition... 
[    7.351156] c0 n = 9
[    7.351158] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351161] c0 Skip verify chain_partition[l_ldsp] while load verify l_gdsp partition... 
[    7.351164] c0 n = 10
[    7.351166] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351170] c0 chain partition founded: n = 11
[    7.351173] c0 [kbc]read partition name: l_gdsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.351177] c0 [kbc]read footer
[    7.351178] c0 dump footer
[    7.351180] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.351195] c0 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
[    7.351209] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.351224] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.351239] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.351243] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_gdsp, offset = 0x24e000, num_bytes = 0x840
[    7.351248] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.351251] c0 [kbc]read certificate: offset = 0x24e000
[    7.351254] c0 dump certificate
[    7.351256] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.351271] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.354006] c0 partition: l_gdsp vbmeta_verify_ret is :0. 
[    7.354020] c0 enter implement read_rollback_index().
[    7.354023] c0 read_is_device_unlocked() rollback_index_slot = 13 
[    7.354026] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.354031] c0 Info: g_sprd_vboot_version.img_ver[13]= 0
[    7.354038] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.354042] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.354049] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.354051] c0 check dat cp
[    7.367713] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.367724] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.367729] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.367735] c0 Skip verify chain_partition[pm_sys] while load verify l_gdsp partition... 
[    7.367739] c0 n = 12
[    7.367741] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.367746] c0 Skip verify chain_partition[l_agdsp] while load verify l_gdsp partition... 
[    7.367749] c0 n = 13
[    7.367752] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367755] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367759] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367762] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367766] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367769] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367774] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.367779] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.367790] c0 read_is_device_unlocked() ret = 0 
[    7.368349] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.368356] c0 partition: vbmeta  guid_buf_size = 37 
[    7.368359] c0 guid: 1.0 
[    7.368367] c0 in avb_slot_verify, l:1626. 
[    7.368370] c0 in avb_slot_verify, l:1640. 
[    7.368374] c0 [kbc]avb_slot_verify result is 0.
[    7.368376] c0 [kbc]l_gdsp_avb_slot_verify result is OK.
[    7.368379] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.368382] c0 [kbc]slot_data[1] is 0x0.
[    7.368389] c0 [kbc]ret = 0
[    7.368441] c0 [kbc]call mem map:ns addr = 0x88040000 map len = 0x200000 
[    7.368483] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.368486] c0 pAddr:
[    7.368488] c0 0000 df f8 94 d0 25 49 26 4a 26 4b 91 42 06 d0 9a 42
[    7.368502] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.368505] c0 [kbc]start verify... 
[    7.368507] c0 [kbc]avb_check_kbc_image() name = pm_sys
[    7.368510] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = pm_sys
[    7.368513] c0 ### enter avb_slot_verify. ###
[    7.368519] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.368543] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.368548] c0 enter implement read_rollback_index().
[    7.368549] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.368552] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.368555] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.368561] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.368565] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368569] c0 Skip verify chain_partition[boot] while load verify pm_sys partition... 
[    7.368572] c0 n = 0
[    7.368574] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368577] c0 Skip verify chain_partition[dtbo] while load verify pm_sys partition... 
[    7.368583] c0 n = 1
[    7.368585] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368589] c0 Skip verify chain_partition[init_boot] while load verify pm_sys partition... 
[    7.368592] c0 n = 2
[    7.368594] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368597] c0 Skip verify chain_partition[vbmeta_odm] while load verify pm_sys partition... 
[    7.368600] c0 n = 3
[    7.368602] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368605] c0 Skip verify chain_partition[vbmeta_product] while load verify pm_sys partition... 
[    7.368609] c0 n = 4
[    7.368610] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368614] c0 Skip verify chain_partition[vbmeta_system] while load verify pm_sys partition... 
[    7.368617] c0 n = 5
[    7.368618] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368622] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify pm_sys partition... 
[    7.368625] c0 n = 6
[    7.368627] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368630] c0 Skip verify chain_partition[vbmeta_vendor] while load verify pm_sys partition... 
[    7.368633] c0 n = 7
[    7.368635] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368698] c0 Skip verify chain_partition[vendor_boot] while load verify pm_sys partition... 
[    7.368702] c0 n = 8
[    7.368704] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368708] c0 Skip verify chain_partition[l_modem] while load verify pm_sys partition... 
[    7.368711] c0 n = 9
[    7.368713] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368716] c0 Skip verify chain_partition[l_ldsp] while load verify pm_sys partition... 
[    7.368719] c0 n = 10
[    7.368721] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368725] c0 Skip verify chain_partition[l_gdsp] while load verify pm_sys partition... 
[    7.368728] c0 n = 11
[    7.368729] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368733] c0 chain partition founded: n = 12
[    7.368737] c0 [kbc]read partition name: pm_sys, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.368742] c0 [kbc]read footer
[    7.368743] c0 dump footer
[    7.368745] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.368760] c0 0010 00 0b a0 08 00 00 00 00 00 0b b0 00 00 00 00 00
[    7.368775] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.368789] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.368805] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.368809] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: pm_sys, offset = 0xbb000, num_bytes = 0x840
[    7.368814] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.368817] c0 [kbc]read certificate: offset = 0xbb000
[    7.368821] c0 dump certificate
[    7.368823] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.368838] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.372090] c0 partition: pm_sys vbmeta_verify_ret is :0. 
[    7.372108] c0 enter implement read_rollback_index().
[    7.372110] c0 read_is_device_unlocked() rollback_index_slot = 14 
[    7.372114] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.372117] c0 Info: g_sprd_vboot_version.img_ver[14]= 0
[    7.372125] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.372129] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.372136] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.372138] c0 check dat cp
[    7.377217] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.377229] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.377233] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.377238] c0 Skip verify chain_partition[l_agdsp] while load verify pm_sys partition... 
[    7.377242] c0 n = 13
[    7.377244] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377248] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377251] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377255] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377258] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377262] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377267] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.377272] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.377282] c0 read_is_device_unlocked() ret = 0 
[    7.377821] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.377828] c0 partition: vbmeta  guid_buf_size = 37 
[    7.377831] c0 guid: 1.0 
[    7.377839] c0 in avb_slot_verify, l:1626. 
[    7.377842] c0 in avb_slot_verify, l:1640. 
[    7.377845] c0 [kbc]avb_slot_verify result is 0.
[    7.377848] c0 [kbc]pm_sys_avb_slot_verify result is OK.
[    7.377851] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.377854] c0 [kbc]slot_data[1] is 0x0.
[    7.377860] c0 [kbc]ret = 0
[    7.377892] c0 [kbc]verify success. 
[    7.377893] c0 version:
[    7.377896] c0 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377911] c0 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377926] c0 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377941] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377956] c0 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378011] c0 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378026] c0 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378041] c0 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378055] c0 0080 00 00 00 00 00 00 00 00
[    7.378064] c0 kbc_v2_dump_version 
[    7.378066] c0 imgver[0] = 0x0 
[    7.378068] c0 imgver[1] = 0x0 
[    7.378070] c0 imgver[2] = 0x0 
[    7.378073] c0 imgver[3] = 0x0 
[    7.378075] c0 imgver[4] = 0x0 
[    7.378077] c0 imgver[5] = 0x0 
[    7.378079] c0 imgver[6] = 0x0 
[    7.378082] c0 imgver[7] = 0x0 
[    7.378084] c0 imgver[8] = 0x0 
[    7.378086] c0 imgver[9] = 0x0 
[    7.378088] c0 imgver[10] = 0x0 
[    7.378090] c0 imgver[11] = 0x0 
[    7.378093] c0 imgver[12] = 0x0 
[    7.378095] c0 imgver[13] = 0x0 
[    7.378097] c0 imgver[14] = 0x0 
[    7.378099] c0 imgver[15] = 0x0 
[    7.378102] c0 imgver[16] = 0x0 
[    7.378104] c0 imgver[17] = 0x0 
[    7.378106] c0 imgver[18] = 0x0 
[    7.378108] c0 imgver[19] = 0x0 
[    7.378111] c0 imgver[20] = 0x0 
[    7.378113] c0 imgver[21] = 0x0 
[    7.378115] c0 imgver[22] = 0x0 
[    7.378117] c0 imgver[23] = 0x0 
[    7.378119] c0 imgver[24] = 0x0 
[    7.378122] c0 imgver[25] = 0x0 
[    7.378124] c0 imgver[26] = 0x0 
[    7.378126] c0 imgver[27] = 0x0 
[    7.378128] c0 imgver[28] = 0x0 
[    7.378130] c0 imgver[29] = 0x0 
[    7.378133] c0 imgver[30] = 0x0 
[    7.378135] c0 imgver[31] = 0x0 
<    7.378143> trusty_kernelbootcp: 185: TA:kbc_verify_all_avb2() ret = 0
[    7.378159] c0 enter SEC_KBC_GET_VERSION
[    7.378162] c0 reset update version flag
<    7.378164> version
<    7.378170> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378178> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378186> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378193> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378201> 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378209> 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378217> 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378224> 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378231> 0080 00 00 00 00 00 00 00 00
<    7.378237> trusty_kernelbootcp: 193: TA:update version flag = 0
[    7.378244] c0 enter SEC_KBC_START_CP
[    7.378248] c0 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[    7.378252] c0 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[    7.378255] c0 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[    7.378258] c0 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[    7.378262] c0 dump_table() flag = 31 
[    7.378264] c0 dump_table() is_packed = 0 
[    7.378266] c0 kbc_start_cp() enter MODEM_IMG
[    7.378268] c0 reg_addr = 0xffffffffe61cc330
[    7.378275] c0 before reg = 2010101
[    7.378280] c0 after  reg = 10101
[    7.378282] c0 sleep 50ms start 
[    7.428355] c0 sleep end 
[    7.428363] c0 reg_addr = 0xffffffffe61cc818
[    7.428371] c0 before reg = 0006
[    7.428377] c0 after  reg = 0004
[    7.428380] c0 sleep 50ms start 
[    7.478897] c0 sleep end 
[    7.478905] c0 reg_addr = 0xffffffffe61ccb98
[    7.478913] c0 before reg = 4800
[    7.478919] c0 after  reg = 4000
[    7.478921] c0 sleep 50ms start 
[    7.529012] c0 sleep end 
[    7.529021] c0 reg_addr = 0xffffffffe61dc174
[    7.529028] c0 before reg = 0400
[    7.529035] c0 after  reg = 0000
[    7.529037] c0 sleep 50ms start 
[    7.584130] c0 sleep end 
[    7.584141] c0 reg_addr = 0xffffffffe61dc08c
[    7.584148] c0 before reg = 0001
[    7.584155] c0 after  reg = 0000
[    7.584158] c0 sleep 50ms start SP_IMG
[    7.634256] c0 sleep end 
[    7.634264] c0 kbc_start_cp() leave 
<    7.634273> trusty_kernelbootcp: 199: TA:SEC_KBC_START_CP() ret = 0
<    7.634293> trusty_kernelbootcp: 202: TA:SEC_PREPARE_FIREWALL_DATA() ret = 0
<    7.634308> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
<   16.203866> storage_client: 316: storage_open_file: invalid  rc = -2 
<   16.203886> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   16.204186> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   16.204202> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 
<   16.207846> storage_client: 316: storage_open_file: invalid  rc = -2 
<   16.207865> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   16.208167> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   16.208183> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 

trusty_keymasterapp/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<   26.187814> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.187828> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.196136> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.196149> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.210954> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.210968> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   26.225371> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.225386> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.239814> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.239830> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.908912> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.908923> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.909056> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.909065> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.909203> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.909211> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   26.910185> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.910197> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.912230> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.912245> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   28.942061> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942074> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   28.942205> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942213> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   28.942349> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942358> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   28.943353> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.943365> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   28.945424> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.945440> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   33.496769> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.496780> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   33.496915> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.496923> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   33.497060> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.497069> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   33.498040> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.498052> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   33.500002> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.500106> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   34.596378> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596425> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   34.596617> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596659> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   34.596852> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596861> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   34.598633> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.598649> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   34.602159> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.602177> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   39.008705> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.008716> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   39.008850> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.008858> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   39.008995> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.009002> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   39.010055> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.010069> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   39.012124> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.012140> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   40.568194> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568204> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   40.568295> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568302> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   40.568398> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568404> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   40.568993> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.569000> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   40.570184> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.570193> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  154.617015> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617027> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  154.617158> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617166> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  154.617304> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617312> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<  154.618438> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.618453> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  154.620654> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.620674> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  155.314374> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314387> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  155.314523> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314532> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  155.314669> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314678> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<  155.315661> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.315673> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  155.317891> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.317906> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 2279.452577> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.452594> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.549907> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.549923> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.551361> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.551378> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.596377> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.596393> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.598215> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.598231> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
meta  guid_buf_size = 37 
[    2.090942] c0 guid: 1.0 
[    2.090947] c0 in avb_slot_verify, l:1626. 
[    2.090949] c0 in avb_slot_verify, l:1640. 
[    2.090951] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.090953] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.090955] c0 slot_data[0] is 0xe04c5e08.
[    2.090957] c0 slot_data[1] is 0x0.
[    2.090958] c0 vboot_verify_ret is :0
[    2.090962] c0 enter copy just debug... 344.
[    2.090964] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20672 androidboot.vbmeta.digest=f45b1135c3f4b119a6ae042e4cc8506ab9d54359bc0e8d536c44
[    2.150231] c1 have got sip smc all from uboot###
[    2.150236] c1  args->params[0] is 0xa. ###
[    2.150343] c1 process_sip_call, case FUNCTYPE_GET_LCS res:0, tmp is 1
[    2.201534] c0 have got sip smc all from uboot###
[    2.201537] c0  args->params[0] is 0x36. ###
[    2.201540] c0 from uboot smc...addr:0x9f140000 len:0x400
[    2.201567] c0 xing offset = 25.
[    2.201570] c0 xing offset2 = 64.
[    2.201596] c0 xing offset = 25.
[    2.201598] c0 xing offset2 = 64.
[    2.205668] c0 have got sip smc all from uboot###
[    2.205670] c0  args->params[0] is 0x40. ###
[    2.205673] c0 from uboot smc(set chip uid)...addr:0x9f13f000 len:0x8
[    2.205677] c0 get cpu id: 0x743a41 0x60d90a8
[    2.205681] c0 chip uid from uboot(id=5) len=8:
[    2.205682] c0 41 3a 74 00 a8 90 0d 06 
[    2.205690] c0 key[5] data has been saved!
[    2.205722] c0 have got sip smc all from uboot###
[    2.205727] c0  args->params[0] is 0x22. ###
[    2.205732] c0 enter set rpmb 16777216
[    2.207592] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207610] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207666] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207680] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207783] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207793] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
<    2.207872> ss: sec_rpmb_bl_client_handle_msg: use new key
[    2.207895] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207906] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207917] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
<    2.207927> ss-ipc: 185: do_disconnect ev->handle ox3ea
<    2.207935> ss: sec_rpmb_bl_disconnect: handle 0x3ea
[    2.207949] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.207965] c0 have got sip smc all from uboot###
[    2.207967] c0  args->params[0] is 0x25. ###
[    2.207969] c0 set rpmb type 205
[    2.208007] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208027] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208077] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208090] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208126] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208135] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208154] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208194] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208213] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208225] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208255] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208263] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208281] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208316] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208337] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208348] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208380] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208388] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208405] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f8e6300: pa=0x9f8e6000: mair=0xff, sharable=0x3
[    2.208439] c0 have got sip smc all from uboot###
[    2.208441] c0  args->params[0] is 0x18. ###
[    2.208457] c0 process_sip_call, case FUNCTYPE_CHECK_CPU_FLASH_BIND, res is:1
[    2.771249] c3 entering scheduler on cpu 3
[    2.772524] c4 entering scheduler on cpu 4
[    2.773684] c5 entering scheduler on cpu 5
[    2.774745] c6 entering scheduler on cpu 6
[    2.775767] c7 entering scheduler on cpu 7
[    5.827576] c3 tam_load_request:1513: load look up com.android.trusty.gatekeeper
[    5.827588] c3 handle_conn_req:412: failed (-2) to send response
<    5.829748> ss: block_device_tipc_ns_init: ns device's block size 4040 block count 1048576
[    5.833326] c7 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    5.833355] c7 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    5.833386] c7 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    5.833389] c7 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    5.833392] c7 ta_manager_verify_img:447: RSA_hash
[    5.834950] c7 ta_manager_verify_img:506: RSA_verify
<    5.835401> block_cache_complete_read: load block 0 failed
<    5.835413> block_cache_load_entry: failed to load block 0
[    5.837903] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    5.837916] c0 trusty_app:(32) start 0xffffffffe0796000 size 0x00017000
[    5.837926] c0 trusty_app: whitelist.table 0x0, size: 0
[    5.837930] c0 trusty_app 11 uuid: 0x38ba0cdc 0xdf0e 0x11e4 0x9869 0x233fb6ae4795
[    5.837938] c0 trusty_app 0xffffffffe06c5e28: stack_sz=0x1000
[    5.837942] c0 trusty_app 0xffffffffe06c5e28: heap_sz=0x2000
[    5.837945] c0 trusty_app 0xffffffffe06c5e28: one_shot=0x0
[    5.837948] c0 trusty_app 0xffffffffe06c5e28: keep_alive=0x0
[    5.837951] c0 trusty_app 0xffffffffe06c5e28: flags=0x1c
[    5.837954] c0 ta_manager_write_ta:985: enter tam anti rollback
[    5.837962] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    5.837966] c0 ta_manager_write_ta:997: tam anti rollback ok
[    5.837969] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    5.837973] c0 trusty_tapp_init:
[    5.838057] c0 trusty_app 11: code: start 0x00008000 end 0x0001aa1c
[    5.838062] c0 trusty_app 11: data: start 0x0001b000 end 0x0001c000
[    5.838067] c0 trusty_app 11: bss:                end 0x0001b420
[    5.838071] c0 trusty_app 11: brk:  start 0x0001c000 end 0x0001e000
[    5.838075] c0 trusty_app 11: entry 0x0000b418
<    5.838094> trusty_gatekeeper_ta: 304: Initializing
<    5.838154> trusty_gatekeeper_ta: 89: ReseedRng
<    5.838525> trusty_gatekeeper_ta: 97: ReseedRng ok
[    5.838558] c0 tam_port_publish:1496: publish port com.android.trusty.gatekeeper
[    5.838590] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.gatekeeper accomplished!
<    5.839043> block_cache_complete_read: load block 1 failed
<    5.839057> block_cache_load_entry: failed to load block 1
<    5.839066> fs_init_from_super:641: clear requested, create empty, version 0
<    5.839690> ss: block_device_tipc_ns_init: create port com.android.trusty.storage.client.td success
<    5.854255> ss: cal_rpmb_block_size: rpmb write data-size 512 to addr (65530) sucessfull
<    5.854273> ss: cal_rpmb_block_size: set rpmb block size 512
<    5.860979> trusty_kernelbootcp: 75: cmd KERNEL_BOOTCP_UNLOCK_DDR
<    5.860994> trusty_kernelbootcp: 322: TA:kbc_unlock_ddr() 
[    5.861002] c1 enter SEC_KBC_GET_TEECFG_FLAG
[    5.861012] c1 pal:g_wifionly_flag = 0 
<    5.861015> trusty_kernelbootcp: 331: TA:g_ioctl_cunter = 1,g_wifionly_flag = 0
[    5.861023] c1 enter SEC_KBC_STOP_CP
[    5.861026] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861030] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861033] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861036] c1 dump_table() len = 0 maplen = 0 addr = 0 
[    5.861039] c1 dump_table() flag = 31 
[    5.861041] c1 dump_table() is_packed = 0 
[    5.861043] c1 kbc_stop_cp() enter MODEM_IMG
[    5.861045] c1 reg_addr = 0xffffffffe61dc174
[    5.861052] c1 before reg = 0400
[    5.861057] c1 after  reg = 0400
[    5.861060] c1 sleep 50ms start 
<    5.861124> ss: block_device_tipc_rpmb_init: rpmb device's block size 512 block count 32754
<    5.868132> fs_init_from_super:603: super block: files at 74 free at 75
<    5.875569> fs_init_from_super:634: loaded super block version 1
<    5.875607> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tp success
<    5.875621> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tdea success
<    5.875628> ss: block_device_tipc_rpmb_init: rb device's block size 4040 block count 1048576
<    5.881561> super_block_valid:344: bad magic, 0x2d64cd1b6a6974ae
<    5.884340> super_block_valid:344: bad magic, 0x2d64cd1b6a6974ae
<    5.884358> fs_init_from_super:641: clear requested, create empty, version 0
<    5.884404> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tr success
<    5.887127> sprdimgversion: sprdimgverion_create_port: rpmb read image/mode image version blk (65534,65532) successful.
<    5.887165> ss: rpmb_proxy_connect: create port com.spreadtrum.sprdimgversion success
<    5.888753> sec_rpmbdata: sec_rpmbdata_create_port: rpmb read sec rpmb data (13, 1) (65517, 65529) successful.
<    5.888792> ss: rpmb_proxy_connect: create port com.spreadtrum.secrpmbdata success
[    5.911576] c1 reg_addr = 0xffffffffe61ccb98
[    5.911590] c1 before reg = 4800
[    5.911595] c1 after  reg = 4800
[    5.911597] c1 sleep 50ms start 
[    5.962358] c1 reg_addr = 0xffffffffe61cc818
[    5.962371] c1 before reg = 0006
[    5.962377] c1 after  reg = 0006
[    5.962380] c1 sleep 50ms start 
[    6.012455] c1 reg_addr = 0xffffffffe61cc330
[    6.012470] c1 before reg = 2010101
[    6.012476] c1 after  reg = 2010101
[    6.012479] c1 sleep 50ms start 
[    6.062549] c1 reg_addr = 0xffffffffe61dc08c
[    6.062562] c1 before reg = 0001
[    6.062568] c1 after  reg = 0001
[    6.062571] c1 sleep 50ms start SP_IMG
[    6.112749] c1 sleep end 
[    6.113106] c1 kbc_stop_cp() leave 
[    6.113116] c1 enter SEC_KBC_GET_LOAD_MODEM_FLAG
[    6.113119] c1 pal: g_load_modem_flag = 0 
<    6.113124> trusty_kernelbootcp: 339: TA:g_ioctl_cunter = 2,g_load_modem_flag = 0
<    6.113138> trusty_kernelbootcp: 340: TA:SEC_KBC_STOP_CP() ret = 0
<    6.113145> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113153> footer
<    6.113157> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113165> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113172> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113180> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113188> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113194> footer
<    6.113198> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113205> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113213> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113220> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113228> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113234> footer
<    6.113237> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113245> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113252> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113260> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113267> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<    6.113273> footer
<    6.113277> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113284> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113292> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113299> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    6.113307> trusty_kernelbootcp: 287: dump_table() flag = 31 
<    6.113313> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
<    6.113379> trusty_kernelbootcp: 348: TA:SEC_FIREWALL_UNLOCK_CP_DDR() ret = 0
<    6.113393> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
[    6.332363] c2 tam_load_request:1513: load look up com.android.trusty.identity
[    6.332376] c2 handle_conn_req:412: failed (-2) to send response
[    6.343646] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    6.343682] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.347052] c3 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    6.347066] c3 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.347070] c3 ta_manager_verify_img:447: RSA_hash
[    6.353103] c0 ta_manager_verify_img:506: RSA_verify
[    6.356245] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    6.356258] c0 trusty_app:(32) start 0xffffffffe07cf000 size 0x0008b000
[    6.356269] c0 trusty_app: whitelist.table 0x0, size: 0
[    6.356310] c0 trusty_app 12 uuid: 0x3f3010ec 0xfc8 0xc8a2 0x9110 0xc5ef1de1233a
[    6.356319] c0 trusty_app 0xffffffffe06bee48: stack_sz=0x40000
[    6.356323] c0 trusty_app 0xffffffffe06bee48: heap_sz=0x40000
[    6.356326] c0 trusty_app 0xffffffffe06bee48: one_shot=0x0
[    6.356330] c0 trusty_app 0xffffffffe06bee48: keep_alive=0x0
[    6.356333] c0 trusty_app 0xffffffffe06bee48: flags=0x1c
[    6.356336] c0 ta_manager_write_ta:985: enter tam anti rollback
[    6.356344] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    6.356349] c0 ta_manager_write_ta:997: tam anti rollback ok
[    6.356352] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    6.356356] c0 trusty_tapp_init:
[    6.356497] c0 trusty_app 12: code: start 0x00008000 end 0x0008e3c4
[    6.356502] c0 trusty_app 12: data: start 0x0008f000 end 0x00090000
[    6.356506] c0 trusty_app 12: bss:                end 0x0008fa14
[    6.356510] c0 trusty_app 12: brk:  start 0x00090000 end 0x000d0000
[    6.356514] c0 trusty_app 12: entry 0x00011130
[    6.356553] c0 tam_port_publish:1501:  other port com.android.trusty.identity.secure
[    6.356570] c0 tam_port_publish:1496: publish port com.android.trusty.identity
[    6.356688] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.identity accomplished!
<    6.623423> ss: block_device_tipc_prodnv_init: prodnv device's block size 4040 block count 1048576
<    6.659762> fs_init_from_super:603: super block: files at 0 free at 2
<    6.659795> fs_init_from_super:605: probe super block's files block [0] failed, fs is corrupted.
<    6.660270> fs_init_from_super:634: loaded super block version 2
<    6.660309> ss: block_device_tipc_prodnv_init: create port com.android.trusty.storage.client.tn success
[    6.955852] c5 tam_load_request:1513: load look up com.android.trusty.faceid
[    6.955864] c5 handle_conn_req:412: failed (-2) to send response
[    6.963676] c0 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[    6.963713] c0 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.963743] c0 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[    6.963747] c0 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[    6.963751] c0 ta_manager_verify_img:447: RSA_hash
[    6.964345] c0 ta_manager_verify_img:506: RSA_verify
[    6.965909] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[    6.965920] c0 trusty_app:(32) start 0xffffffffe08ee000 size 0x00015000
[    6.965931] c0 trusty_app: whitelist.table 0x0, size: 0
[    6.965935] c0 trusty_app 13 uuid: 0xf4bc36e6 0x8ec2 0x46e2 0xa82e 0xf7cb6cdc6f72
[    6.965944] c0 trusty_app 0xffffffffe0723de0: stack_sz=0x2000
[    6.965947] c0 trusty_app 0xffffffffe0723de0: heap_sz=0x6000
[    6.965950] c0 trusty_app 0xffffffffe0723de0: one_shot=0x0
[    6.965953] c0 trusty_app 0xffffffffe0723de0: keep_alive=0x0
[    6.965957] c0 trusty_app 0xffffffffe0723de0: flags=0x1c
[    6.965959] c0 ta_manager_write_ta:985: enter tam anti rollback
[    6.965967] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[    6.965971] c0 ta_manager_write_ta:997: tam anti rollback ok
[    6.965975] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[    6.965980] c0 trusty_tapp_init:
[    6.966056] c0 trusty_app 13: code: start 0x00008000 end 0x000189cc
[    6.966061] c0 trusty_app 13: data: start 0x00019000 end 0x0001a000
[    6.966067] c0 trusty_app 13: bss:                end 0x0001941c
[    6.966070] c0 trusty_app 13: brk:  start 0x0001a000 end 0x00020000
[    6.966075] c0 trusty_app 13: entry 0x0000b430
[    6.966113] c0 tam_port_publish:1496: publish port com.android.trusty.faceid
[    6.966127] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.faceid accomplished!
<    7.180371> trusty_kernelbootcp: 67: cmd KERNEL_BOOTCP_VERIFY_ALL
<    7.180386> trusty_kernelbootcp: 177: TA:kbc_verify_all_avb2() 
<    7.180392> trusty_kernelbootcp: 281: dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
<    7.180401> footer
<    7.180405> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180413> 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
<    7.180420> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180428> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180436> trusty_kernelbootcp: 281: dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
<    7.180443> footer
<    7.180446> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180454> 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
<    7.180461> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180469> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180476> trusty_kernelbootcp: 281: dump_table() len = 440000 maplen = 440000 addr = 89620000 
<    7.180483> footer
<    7.180487> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180494> 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
<    7.180501> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180509> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180516> trusty_kernelbootcp: 281: dump_table() len = 200000 maplen = 200000 addr = 88040000 
<    7.180523> footer
<    7.180527> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<    7.180534> 0010 00 0b a0 08 00 00 00 00 00 0b b0 00 00 00 00 00
<    7.180542> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180549> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.180557> trusty_kernelbootcp: 287: dump_table() flag = 31 
<    7.180562> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
[    7.180572] c3 enter sec_kbc_check_verify_table
<    7.180591> trusty_kernelbootcp: 167: TA:table->flag = 0x1f:
<    7.180660> trusty_kernelbootcp: 182: TA:SEC_FIREWALL_LOCK_CP_DRR() ret = 0
[    7.180667] c3 enter SEC_KBC_VERIFY_ALL_V2
[    7.180670] c3 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[    7.180675] c3 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[    7.180678] c3 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[    7.180682] c3 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[    7.180685] c3 dump_table() flag = 31 
[    7.180688] c3 dump_table() is_packed = 0 
[    7.180691] c3 [kbc]kbc_image_verify_v2() enter.
[    7.180695] c3 [kbc]call mem map:ns addr = 0x8b000000 map len = 0x1380000 
[    7.181027] c3 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.181031] c3 pAddr:
[    7.181033] c3 0000 4d 45 43 50 56 31 2e 30 00 06 00 00 00 00 00 00
[    7.181048] c3 [kbc]avb_userdata_set() packed offset = 0x0
[    7.181051] c3 [kbc]start verify... 
[    7.181052] c3 [kbc]avb_check_kbc_image() name = l_modem
[    7.181058] c3 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_modem
[    7.181062] c3 ### enter avb_slot_verify. ###
[    7.181071] c3 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.181096] c3 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.183880] c3 partition: vbmeta vbmeta_verify_ret is :0. 
[    7.183885] c3 Enter: implement validate_vbmeta_public_key().
[    7.183887] c3 dump public_key_hash: public_key_hash_length:32. 
[    7.183890] c3 cal_sha256(): enter cal_sha256 
[    7.183899] c3 expected_public_key is matched.
[    7.183902] c3 enter implement read_rollback_index().
[    7.183904] c3 read_is_device_unlocked() rollback_index_slot = 0 
[    7.183906] c3 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.183910] c3 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.183916] c3 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.183920] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183927] c3 Skip verify chain_partition[boot] while load verify l_modem partition... 
[    7.183931] c3 n = 0
[    7.183932] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183936] c3 Skip verify chain_partition[dtbo] while load verify l_modem partition... 
[    7.183939] c3 n = 1
[    7.183941] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183944] c3 Skip verify chain_partition[init_boot] while load verify l_modem partition... 
[    7.183947] c3 n = 2
[    7.183949] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183952] c3 Skip verify chain_partition[vbmeta_odm] while load verify l_modem partition... 
[    7.183956] c3 n = 3
[    7.183957] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183961] c3 Skip verify chain_partition[vbmeta_product] while load verify l_modem partition... 
[    7.183964] c3 n = 4
[    7.183966] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183969] c3 Skip verify chain_partition[vbmeta_system] while load verify l_modem partition... 
[    7.183972] c3 n = 5
[    7.183974] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183978] c3 Skip verify chain_partition[vbmeta_system_ext] while load verify l_modem partition... 
[    7.183981] c3 n = 6
[    7.183982] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183986] c3 Skip verify chain_partition[vbmeta_vendor] while load verify l_modem partition... 
[    7.183989] c3 n = 7
[    7.183991] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.183994] c3 Skip verify chain_partition[vendor_boot] while load verify l_modem partition... 
[    7.183997] c3 n = 8
[    7.183999] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.184002] c3 chain partition founded: n = 9
[    7.184101] c3 [kbc]read partition name: l_modem, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.184109] c3 [kbc]read footer
[    7.184111] c3 dump footer
[    7.184113] c3 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.184128] c3 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
[    7.184142] c3 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.184157] c3 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.184172] c3 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.184178] c3 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_modem, offset = 0xd39000, num_bytes = 0x840
[    7.184182] c3 [kbc]read certificate: img_addr = 0xe662e000
[    7.184185] c3 [kbc]read certificate: offset = 0xd39000
[    7.184189] c3 dump certificate
[    7.184191] c3 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.184206] c3 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.186627] c3 partition: l_modem vbmeta_verify_ret is :0. 
[    7.186636] c3 enter implement read_rollback_index().
[    7.186638] c3 read_is_device_unlocked() rollback_index_slot = 11 
[    7.186641] c3 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.186645] c3 Info: g_sprd_vboot_version.img_ver[11]= 0
[    7.186649] c3 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.186653] c3 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.186659] c3 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.186661] c3 check dat cp
[    7.307371] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.307382] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.307387] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307392] c0 Skip verify chain_partition[l_ldsp] while load verify l_modem partition... 
[    7.307395] c0 n = 10
[    7.307397] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307401] c0 Skip verify chain_partition[l_gdsp] while load verify l_modem partition... 
[    7.307404] c0 n = 11
[    7.307406] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307410] c0 Skip verify chain_partition[pm_sys] while load verify l_modem partition... 
[    7.307413] c0 n = 12
[    7.307416] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.307420] c0 Skip verify chain_partition[l_agdsp] while load verify l_modem partition... 
[    7.307423] c0 n = 13
[    7.307425] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307428] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307432] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307435] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307439] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307442] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.307447] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.307451] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.307462] c0 read_is_device_unlocked() ret = 0 
[    7.309408] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.309416] c0 partition: vbmeta  guid_buf_size = 37 
[    7.309419] c0 guid: 1.0 
[    7.309426] c0 in avb_slot_verify, l:1626. 
[    7.309430] c0 in avb_slot_verify, l:1640. 
[    7.309433] c0 [kbc]avb_slot_verify result is 0.
[    7.309435] c0 [kbc]l_modem_avb_slot_verify result is OK.
[    7.309439] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.309441] c0 [kbc]slot_data[1] is 0x0.
[    7.309448] c0 [kbc]ret = 0
[    7.309640] c0 [kbc]call mem map:ns addr = 0x89aa8000 map len = 0xb00000 
[    7.309834] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.309838] c0 pAddr:
[    7.309840] c0 0000 00 5a 5a 5a 08 73 e6 d7 b0 20 00 18 08 db e6 d7
[    7.309855] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.309859] c0 [kbc]start verify... 
[    7.309861] c0 [kbc]avb_check_kbc_image() name = l_ldsp
[    7.309866] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_ldsp
[    7.309869] c0 ### enter avb_slot_verify. ###
[    7.309876] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.309901] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.309905] c0 enter implement read_rollback_index().
[    7.309907] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.309910] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.309913] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.309919] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.309923] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309965] c0 Skip verify chain_partition[boot] while load verify l_ldsp partition... 
[    7.309969] c0 n = 0
[    7.309971] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309974] c0 Skip verify chain_partition[dtbo] while load verify l_ldsp partition... 
[    7.309977] c0 n = 1
[    7.309979] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309983] c0 Skip verify chain_partition[init_boot] while load verify l_ldsp partition... 
[    7.309986] c0 n = 2
[    7.309987] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309991] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_ldsp partition... 
[    7.309994] c0 n = 3
[    7.309996] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.309999] c0 Skip verify chain_partition[vbmeta_product] while load verify l_ldsp partition... 
[    7.310002] c0 n = 4
[    7.310004] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310008] c0 Skip verify chain_partition[vbmeta_system] while load verify l_ldsp partition... 
[    7.310011] c0 n = 5
[    7.310013] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310016] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_ldsp partition... 
[    7.310019] c0 n = 6
[    7.310021] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310025] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_ldsp partition... 
[    7.310028] c0 n = 7
[    7.310029] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310033] c0 Skip verify chain_partition[vendor_boot] while load verify l_ldsp partition... 
[    7.310036] c0 n = 8
[    7.310038] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310041] c0 Skip verify chain_partition[l_modem] while load verify l_ldsp partition... 
[    7.310045] c0 n = 9
[    7.310046] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.310050] c0 chain partition founded: n = 10
[    7.310054] c0 [kbc]read partition name: l_ldsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.310058] c0 [kbc]read footer
[    7.310060] c0 dump footer
[    7.310062] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.310119] c0 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
[    7.310134] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.310148] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.310163] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.310169] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_ldsp, offset = 0x300000, num_bytes = 0x840
[    7.310174] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.310177] c0 [kbc]read certificate: offset = 0x300000
[    7.310181] c0 dump certificate
[    7.310183] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.310198] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.314329] c0 partition: l_ldsp vbmeta_verify_ret is :0. 
[    7.314346] c0 enter implement read_rollback_index().
[    7.314348] c0 read_is_device_unlocked() rollback_index_slot = 12 
[    7.314351] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.314355] c0 Info: g_sprd_vboot_version.img_ver[12]= 0
[    7.314362] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.314366] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.314374] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.314377] c0 check dat cp
[    7.350245] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.350255] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.350260] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350266] c0 Skip verify chain_partition[l_gdsp] while load verify l_ldsp partition... 
[    7.350269] c0 n = 11
[    7.350271] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350275] c0 Skip verify chain_partition[pm_sys] while load verify l_ldsp partition... 
[    7.350278] c0 n = 12
[    7.350280] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.350284] c0 Skip verify chain_partition[l_agdsp] while load verify l_ldsp partition... 
[    7.350287] c0 n = 13
[    7.350289] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350293] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350296] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350300] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350303] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350306] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.350311] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.350317] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.350327] c0 read_is_device_unlocked() ret = 0 
[    7.350774] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.350779] c0 partition: vbmeta  guid_buf_size = 37 
[    7.350783] c0 guid: 1.0 
[    7.350789] c0 in avb_slot_verify, l:1626. 
[    7.350792] c0 in avb_slot_verify, l:1640. 
[    7.350796] c0 [kbc]avb_slot_verify result is 0.
[    7.350798] c0 [kbc]l_ldsp_avb_slot_verify result is OK.
[    7.350801] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.350804] c0 [kbc]slot_data[1] is 0x0.
[    7.350810] c0 [kbc]ret = 0
[    7.350905] c0 [kbc]call mem map:ns addr = 0x89620000 map len = 0x440000 
[    7.350988] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.350991] c0 pAddr:
[    7.350993] c0 0000 2e 50 53 44 38 38 43 53 5f 53 30 30 00 4d 53 47
[    7.351008] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.351010] c0 [kbc]start verify... 
[    7.351012] c0 [kbc]avb_check_kbc_image() name = l_gdsp
[    7.351016] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_gdsp
[    7.351020] c0 ### enter avb_slot_verify. ###
[    7.351027] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.351052] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.351056] c0 enter implement read_rollback_index().
[    7.351057] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.351060] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.351064] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.351070] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.351074] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351078] c0 Skip verify chain_partition[boot] while load verify l_gdsp partition... 
[    7.351081] c0 n = 0
[    7.351083] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351087] c0 Skip verify chain_partition[dtbo] while load verify l_gdsp partition... 
[    7.351090] c0 n = 1
[    7.351091] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351095] c0 Skip verify chain_partition[init_boot] while load verify l_gdsp partition... 
[    7.351098] c0 n = 2
[    7.351100] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351103] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_gdsp partition... 
[    7.351106] c0 n = 3
[    7.351108] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351111] c0 Skip verify chain_partition[vbmeta_product] while load verify l_gdsp partition... 
[    7.351115] c0 n = 4
[    7.351116] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351120] c0 Skip verify chain_partition[vbmeta_system] while load verify l_gdsp partition... 
[    7.351123] c0 n = 5
[    7.351124] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351128] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_gdsp partition... 
[    7.351131] c0 n = 6
[    7.351133] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351136] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_gdsp partition... 
[    7.351139] c0 n = 7
[    7.351141] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351145] c0 Skip verify chain_partition[vendor_boot] while load verify l_gdsp partition... 
[    7.351148] c0 n = 8
[    7.351149] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351153] c0 Skip verify chain_partition[l_modem] while load verify l_gdsp partition... 
[    7.351156] c0 n = 9
[    7.351158] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351161] c0 Skip verify chain_partition[l_ldsp] while load verify l_gdsp partition... 
[    7.351164] c0 n = 10
[    7.351166] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.351170] c0 chain partition founded: n = 11
[    7.351173] c0 [kbc]read partition name: l_gdsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.351177] c0 [kbc]read footer
[    7.351178] c0 dump footer
[    7.351180] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.351195] c0 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
[    7.351209] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.351224] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.351239] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.351243] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_gdsp, offset = 0x24e000, num_bytes = 0x840
[    7.351248] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.351251] c0 [kbc]read certificate: offset = 0x24e000
[    7.351254] c0 dump certificate
[    7.351256] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.351271] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.354006] c0 partition: l_gdsp vbmeta_verify_ret is :0. 
[    7.354020] c0 enter implement read_rollback_index().
[    7.354023] c0 read_is_device_unlocked() rollback_index_slot = 13 
[    7.354026] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.354031] c0 Info: g_sprd_vboot_version.img_ver[13]= 0
[    7.354038] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.354042] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.354049] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.354051] c0 check dat cp
[    7.367713] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.367724] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.367729] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.367735] c0 Skip verify chain_partition[pm_sys] while load verify l_gdsp partition... 
[    7.367739] c0 n = 12
[    7.367741] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.367746] c0 Skip verify chain_partition[l_agdsp] while load verify l_gdsp partition... 
[    7.367749] c0 n = 13
[    7.367752] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367755] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367759] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367762] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367766] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367769] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.367774] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.367779] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.367790] c0 read_is_device_unlocked() ret = 0 
[    7.368349] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.368356] c0 partition: vbmeta  guid_buf_size = 37 
[    7.368359] c0 guid: 1.0 
[    7.368367] c0 in avb_slot_verify, l:1626. 
[    7.368370] c0 in avb_slot_verify, l:1640. 
[    7.368374] c0 [kbc]avb_slot_verify result is 0.
[    7.368376] c0 [kbc]l_gdsp_avb_slot_verify result is OK.
[    7.368379] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.368382] c0 [kbc]slot_data[1] is 0x0.
[    7.368389] c0 [kbc]ret = 0
[    7.368441] c0 [kbc]call mem map:ns addr = 0x88040000 map len = 0x200000 
[    7.368483] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[    7.368486] c0 pAddr:
[    7.368488] c0 0000 df f8 94 d0 25 49 26 4a 26 4b 91 42 06 d0 9a 42
[    7.368502] c0 [kbc]avb_userdata_set() packed offset = 0x0
[    7.368505] c0 [kbc]start verify... 
[    7.368507] c0 [kbc]avb_check_kbc_image() name = pm_sys
[    7.368510] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = pm_sys
[    7.368513] c0 ### enter avb_slot_verify. ###
[    7.368519] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[    7.368543] c0 [kbc]read_from_partition_kbc leave buf = 0xe07b1120
[    7.368548] c0 enter implement read_rollback_index().
[    7.368549] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    7.368552] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.368555] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    7.368561] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    7.368565] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368569] c0 Skip verify chain_partition[boot] while load verify pm_sys partition... 
[    7.368572] c0 n = 0
[    7.368574] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368577] c0 Skip verify chain_partition[dtbo] while load verify pm_sys partition... 
[    7.368583] c0 n = 1
[    7.368585] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368589] c0 Skip verify chain_partition[init_boot] while load verify pm_sys partition... 
[    7.368592] c0 n = 2
[    7.368594] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368597] c0 Skip verify chain_partition[vbmeta_odm] while load verify pm_sys partition... 
[    7.368600] c0 n = 3
[    7.368602] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368605] c0 Skip verify chain_partition[vbmeta_product] while load verify pm_sys partition... 
[    7.368609] c0 n = 4
[    7.368610] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368614] c0 Skip verify chain_partition[vbmeta_system] while load verify pm_sys partition... 
[    7.368617] c0 n = 5
[    7.368618] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368622] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify pm_sys partition... 
[    7.368625] c0 n = 6
[    7.368627] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368630] c0 Skip verify chain_partition[vbmeta_vendor] while load verify pm_sys partition... 
[    7.368633] c0 n = 7
[    7.368635] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368698] c0 Skip verify chain_partition[vendor_boot] while load verify pm_sys partition... 
[    7.368702] c0 n = 8
[    7.368704] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368708] c0 Skip verify chain_partition[l_modem] while load verify pm_sys partition... 
[    7.368711] c0 n = 9
[    7.368713] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368716] c0 Skip verify chain_partition[l_ldsp] while load verify pm_sys partition... 
[    7.368719] c0 n = 10
[    7.368721] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368725] c0 Skip verify chain_partition[l_gdsp] while load verify pm_sys partition... 
[    7.368728] c0 n = 11
[    7.368729] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.368733] c0 chain partition founded: n = 12
[    7.368737] c0 [kbc]read partition name: pm_sys, offset = 0xffffffffffffffc0, num_bytes = 0x40
[    7.368742] c0 [kbc]read footer
[    7.368743] c0 dump footer
[    7.368745] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[    7.368760] c0 0010 00 0b a0 08 00 00 00 00 00 0b b0 00 00 00 00 00
[    7.368775] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[    7.368789] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.368805] c0 [kbc]read_from_partition_kbc leave buf = 0xe068a3e8
[    7.368809] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: pm_sys, offset = 0xbb000, num_bytes = 0x840
[    7.368814] c0 [kbc]read certificate: img_addr = 0xe662e000
[    7.368817] c0 [kbc]read certificate: offset = 0xbb000
[    7.368821] c0 dump certificate
[    7.368823] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[    7.368838] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c1138
[    7.372090] c0 partition: pm_sys vbmeta_verify_ret is :0. 
[    7.372108] c0 enter implement read_rollback_index().
[    7.372110] c0 read_is_device_unlocked() rollback_index_slot = 14 
[    7.372114] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    7.372117] c0 Info: g_sprd_vboot_version.img_ver[14]= 0
[    7.372125] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    7.372129] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    7.372136] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[    7.372138] c0 check dat cp
[    7.377217] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    7.377229] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    7.377233] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    7.377238] c0 Skip verify chain_partition[l_agdsp] while load verify pm_sys partition... 
[    7.377242] c0 n = 13
[    7.377244] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377248] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377251] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377255] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377258] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377262] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    7.377267] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    7.377272] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    7.377282] c0 read_is_device_unlocked() ret = 0 
[    7.377821] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    7.377828] c0 partition: vbmeta  guid_buf_size = 37 
[    7.377831] c0 guid: 1.0 
[    7.377839] c0 in avb_slot_verify, l:1626. 
[    7.377842] c0 in avb_slot_verify, l:1640. 
[    7.377845] c0 [kbc]avb_slot_verify result is 0.
[    7.377848] c0 [kbc]pm_sys_avb_slot_verify result is OK.
[    7.377851] c0 [kbc]slot_data[0] is 0xe072ce50.
[    7.377854] c0 [kbc]slot_data[1] is 0x0.
[    7.377860] c0 [kbc]ret = 0
[    7.377892] c0 [kbc]verify success. 
[    7.377893] c0 version:
[    7.377896] c0 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377911] c0 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377926] c0 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377941] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.377956] c0 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378011] c0 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378026] c0 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378041] c0 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[    7.378055] c0 0080 00 00 00 00 00 00 00 00
[    7.378064] c0 kbc_v2_dump_version 
[    7.378066] c0 imgver[0] = 0x0 
[    7.378068] c0 imgver[1] = 0x0 
[    7.378070] c0 imgver[2] = 0x0 
[    7.378073] c0 imgver[3] = 0x0 
[    7.378075] c0 imgver[4] = 0x0 
[    7.378077] c0 imgver[5] = 0x0 
[    7.378079] c0 imgver[6] = 0x0 
[    7.378082] c0 imgver[7] = 0x0 
[    7.378084] c0 imgver[8] = 0x0 
[    7.378086] c0 imgver[9] = 0x0 
[    7.378088] c0 imgver[10] = 0x0 
[    7.378090] c0 imgver[11] = 0x0 
[    7.378093] c0 imgver[12] = 0x0 
[    7.378095] c0 imgver[13] = 0x0 
[    7.378097] c0 imgver[14] = 0x0 
[    7.378099] c0 imgver[15] = 0x0 
[    7.378102] c0 imgver[16] = 0x0 
[    7.378104] c0 imgver[17] = 0x0 
[    7.378106] c0 imgver[18] = 0x0 
[    7.378108] c0 imgver[19] = 0x0 
[    7.378111] c0 imgver[20] = 0x0 
[    7.378113] c0 imgver[21] = 0x0 
[    7.378115] c0 imgver[22] = 0x0 
[    7.378117] c0 imgver[23] = 0x0 
[    7.378119] c0 imgver[24] = 0x0 
[    7.378122] c0 imgver[25] = 0x0 
[    7.378124] c0 imgver[26] = 0x0 
[    7.378126] c0 imgver[27] = 0x0 
[    7.378128] c0 imgver[28] = 0x0 
[    7.378130] c0 imgver[29] = 0x0 
[    7.378133] c0 imgver[30] = 0x0 
[    7.378135] c0 imgver[31] = 0x0 
<    7.378143> trusty_kernelbootcp: 185: TA:kbc_verify_all_avb2() ret = 0
[    7.378159] c0 enter SEC_KBC_GET_VERSION
[    7.378162] c0 reset update version flag
<    7.378164> version
<    7.378170> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378178> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378186> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378193> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378201> 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378209> 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378217> 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378224> 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<    7.378231> 0080 00 00 00 00 00 00 00 00
<    7.378237> trusty_kernelbootcp: 193: TA:update version flag = 0
[    7.378244] c0 enter SEC_KBC_START_CP
[    7.378248] c0 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[    7.378252] c0 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[    7.378255] c0 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[    7.378258] c0 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[    7.378262] c0 dump_table() flag = 31 
[    7.378264] c0 dump_table() is_packed = 0 
[    7.378266] c0 kbc_start_cp() enter MODEM_IMG
[    7.378268] c0 reg_addr = 0xffffffffe61cc330
[    7.378275] c0 before reg = 2010101
[    7.378280] c0 after  reg = 10101
[    7.378282] c0 sleep 50ms start 
[    7.428355] c0 sleep end 
[    7.428363] c0 reg_addr = 0xffffffffe61cc818
[    7.428371] c0 before reg = 0006
[    7.428377] c0 after  reg = 0004
[    7.428380] c0 sleep 50ms start 
[    7.478897] c0 sleep end 
[    7.478905] c0 reg_addr = 0xffffffffe61ccb98
[    7.478913] c0 before reg = 4800
[    7.478919] c0 after  reg = 4000
[    7.478921] c0 sleep 50ms start 
[    7.529012] c0 sleep end 
[    7.529021] c0 reg_addr = 0xffffffffe61dc174
[    7.529028] c0 before reg = 0400
[    7.529035] c0 after  reg = 0000
[    7.529037] c0 sleep 50ms start 
[    7.584130] c0 sleep end 
[    7.584141] c0 reg_addr = 0xffffffffe61dc08c
[    7.584148] c0 before reg = 0001
[    7.584155] c0 after  reg = 0000
[    7.584158] c0 sleep 50ms start SP_IMG
[    7.634256] c0 sleep end 
[    7.634264] c0 kbc_start_cp() leave 
<    7.634273> trusty_kernelbootcp: 199: TA:SEC_KBC_START_CP() ret = 0
<    7.634293> trusty_kernelbootcp: 202: TA:SEC_PREPARE_FIREWALL_DATA() ret = 0
<    7.634308> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
<   16.203866> storage_client: 316: storage_open_file: invalid  rc = -2 
<   16.203886> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   16.204186> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   16.204202> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 
<   16.207846> storage_client: 316: storage_open_file: invalid  rc = -2 
<   16.207865> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   16.208167> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   16.208183> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 

trusty_keymasterapp/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage file close 
storage session close 
<   26.187814> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.187828> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.196136> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.196149> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.210954> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.210968> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   26.225371> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.225386> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.239814> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.239830> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.908912> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.908923> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.909056> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.909065> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   26.909203> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.909211> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   26.910185> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.910197> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   26.912230> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   26.912245> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   28.942061> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942074> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   28.942205> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942213> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   28.942349> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.942358> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   28.943353> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.943365> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   28.945424> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   28.945440> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   33.496769> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.496780> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   33.496915> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.496923> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   33.497060> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.497069> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   33.498040> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.498052> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   33.500002> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   33.500106> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   34.596378> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596425> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   34.596617> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596659> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   34.596852> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.596861> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   34.598633> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.598649> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   34.602159> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   34.602177> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   39.008705> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.008716> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   39.008850> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.008858> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   39.008995> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.009002> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   39.010055> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.010069> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   39.012124> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   39.012140> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   40.568194> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568204> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   40.568295> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568302> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<   40.568398> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.568404> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<   40.568993> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.569000> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<   40.570184> ss-ipc: 185: do_disconnect ev->handle ox3f5
<   40.570193> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  154.617015> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617027> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  154.617158> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617166> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  154.617304> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.617312> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<  154.618438> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.618453> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  154.620654> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  154.620674> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  155.314374> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314387> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  155.314523> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314532> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
<  155.314669> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.314678> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
storage file close 
storage session close 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
<  155.315661> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.315673> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage file close 
storage session close 
<  155.317891> ss-ipc: 185: do_disconnect ev->handle ox3f5
<  155.317906> ss: disconnect whith 5f902ace5e5c4cd8ae5487b88c22ddaf 
< 2279.452577> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.452594> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.549907> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.549923> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.551361> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.551378> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.596377> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.596393> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
< 2279.598215> ss-ipc: 185: do_disconnect ev->handle ox3f5
< 2279.598231> ss: disconnect whith a755aeacec7d45e098dacd5bdca03f65 
