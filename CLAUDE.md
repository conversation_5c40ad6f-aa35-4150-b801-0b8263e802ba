# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Codebase Overview

This repository contains Android system logs and performance data from a device boot sequence. The logs capture system initialization, hardware detection, and performance metrics during the Android boot process.

## Key Files and Data

1. **Log Files**:
   - `0-android.log` - Android system logs during boot
   - `0-kernel.log` - Kernel-level logs with hardware events
   - `0-sysinfo.log` - System information including memory usage
   - `0-phoneinfo.log` - Device information and kernel parameters
   - `sgm.csv` - Performance metrics including CPU, memory, and I/O statistics

2. **Performance Data**:
   - The `sgm.csv` file contains detailed performance metrics sampled during boot with columns for:
     - CPU usage per core
     - Memory utilization
     - I/O statistics
     - Process information
     - Various system performance counters

## Architecture Notes

This is a diagnostic dataset rather than a traditional codebase. The files represent system state information captured during Android device boot-up, particularly focused on performance analysis and system behavior.

The data appears to be from a device with:
- 8-core CPU (CPU0-CPU7)
- ~3.8GB total RAM
- Android 13 based system (based on kernel version)
- Qualcomm/SPRD based platform (evidenced by sprd-wlan references)

## Common Tasks

Since this is a diagnostic dataset, common tasks would involve:
1. Analyzing boot performance using the CSV data
2. Examining system logs for errors or anomalies
3. Correlating events in different log files
4. Identifying performance bottlenecks during boot

No build or test commands are applicable as this is not a software project repository.