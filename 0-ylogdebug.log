ylogdebug_0  on 08-01 12:01:53


uptime on Fri Aug  1 12:01:53 CST 2025
 12:01:53 up 38 min,  0 users,  load average: 15.99, 14.97, 10.11
run finished on 08-01 12:01:53


logcat -S on Fri Aug  1 12:01:53 CST 2025
size/num main               system             crash              kernel             Total
Total    7583713/57459      2045665/12533      0/0                0/0                9629378/69992
Now      987059/7344        934223/4822        0/0                0/0                1921282/12166
Logspan  25.284             37:47.475                                                37:47.475
Overhead 261809             258699                                                   532978

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               421059
  PID/UID   COMMAND LINE                                       "
  586/1000  /system/bin/surfaceflinger                     148137
 1032/1000  system_server                                  133744
 1365/1000  com.android.settings                            65133
  553/1000 ...ndroid.hardware.graphics.composer@2.4-service 19647
  552/1000 ...droid.hardware.graphics.allocator@4.0-service 18682
  580/1000 /vendor/bin/hw/vendor.sprd.hardware.tool-service 14469
  738/1000  /vendor/bin/phasecheckserver                     7512
  581/1000 ...r/bin/hw/vendor.sprd.hardware.vibrator-service 6780
10121 com.sprd.engineermode                                143920
0     root                                                 129938
10133 com.google.android.dialer                            122686
1041  audioserver                                           71443
10120 com.sprd.logmanager                                   25143
10164 com.google.android.apps.tachyon                       22710
10181 com.android.launcher3                                 11773
10159 com.google.android.inputmethod.latin                   9223
10137 com.google.android.gms                                 9166
10187 com.android.systemui                                   8723
10157 com.google.android.contacts                            4481


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               793077
10187 com.android.systemui                                 126011
0     root                                                   8970

run finished on 08-01 12:01:53


ylogctl q on Fri Aug  1 12:01:53 CST 2025
run finished on 08-01 12:01:53


ylogctl space on Fri Aug  1 12:01:53 CST 2025
run finished on 08-01 12:01:53


cat /data/ylog/ylog.conf on Fri Aug  1 12:01:53 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1
run finished on 08-01 12:01:54


ls -l /data/ylog/ap/ on Fri Aug  1 12:01:54 CST 2025
total 1051
-rw-rw-rw- 1 <USER> <GROUP> 1048953 2025-08-01 12:01 000-0801_120153_poweron.ylog
-rw-rw-rw- 1 <USER> <GROUP>   19006 2025-08-01 12:01 analyzer.py
drwxrwxrwx 4 <USER> <GROUP>    3452 2025-08-01 12:01 blackboxlog
run finished on 08-01 12:01:54


cat /data/ylog/journal.log on Fri Aug  1 12:01:54 CST 2025
[12-31 13:00:40.221] (     1) [     7]     530     530  

.............................
[12-31 13:00:40.222] (     2) [     7]     530     530  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[12-31 13:00:40.227] (     3) [     7]     530     530  /data/ylog/ylog.conf error,reinit it
[12-31 13:00:40.228] (     4) [     7]     530     530  ylog config is /product/etc/ylog.conf.user
[12-31 13:00:40.232] (     5) [     7]     530     530  syncLegcyConfig
[12-31 13:00:40.233] (     6) [     7]     530     530  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[12-31 13:00:40.233] (     7) [     7]     530     530  setSubLog onOff:0 logType:lastlog
[12-31 13:00:40.233] (     8) [     7]     530     530  setSubLog onOff:0 logType:uboot
[12-31 13:00:40.233] (     9) [     7]     530     530  setSubLog onOff:0 logType:android
[12-31 13:00:40.233] (    10) [     7]     530     530  setSubLog onOff:0 logType:kernel
[12-31 13:00:40.233] (    11) [     7]     530     530  setSubLog onOff:0 logType:trace
[12-31 13:00:40.233] (    12) [     7]     530     530  setSubLog onOff:0 logType:sgm
[12-31 13:00:40.233] (    13) [     7]     530     530  setSubLog onOff:0 logType:sysinfo
[12-31 13:00:40.233] (    14) [     7]     530     530  setSubLog onOff:0 logType:thermal
[12-31 13:00:40.233] (    15) [     7]     530     530  setSubLog onOff:0 logType:ylogdebug
[12-31 13:00:40.233] (    16) [     7]     530     530  setSubLog onOff:0 logType:phoneinfo
[12-31 13:00:40.233] (    17) [     7]     530     530  setSubLog onOff:1 logType:hcidump
[12-31 13:00:40.233] (    18) [     7]     530     530  setSubLog onOff:1 logType:tcpdump
[12-31 13:00:40.233] (    19) [     7]     530     530  setSubLog onOff:0 logType:trustlog
[12-31 13:00:40.283] (     1) [     7]     615     615  aplogfilesize : 256
[12-31 13:00:40.284] (     2) [     7]     615     620  LogReboot:startrebootServcie
[12-31 13:00:40.284] (     3) [     7]     615     615  srootdir : default/
[12-31 13:00:40.285] (     4) [     7]     615     615  aplogmaxsize : 99%
[12-31 13:00:40.285] (     5) [     7]     615     615  aplogrotate : 1
[12-31 13:00:40.285] (     6) [     7]     615     615  prioritypath : 0
[12-31 13:00:40.286] (     7) [     7]     615     620  mkdir /data/log/reliability/dumplog/ success
[12-31 13:00:45.286] (     8) [    12]     615     620  currentTime: 20071231130045-23272583
[12-31 13:00:45.287] (     9) [    12]     615     620  SystemBootMode::LINUXKERNEL
[12-31 13:00:45.287] (    10) [    12]     615     620  boot_cause: Reboot into normal
[12-31 13:00:45.287] (    11) [    12]     615     620  boot_reason: normalboot
[12-31 13:00:45.287] (    12) [    12]     615     620  boot_category: normalboot
[12-31 13:00:45.288] (    13) [    12]     615    1041  open /dev/block/by-name/sd_klog failed No such file or directory
[12-31 13:00:45.289] (    14) [    12]     615     620  Crash_reason: Normal
[01-01 11:16:19.228] (     1) [     6]     521     521  

.............................
[01-01 11:16:19.229] (     2) [     6]     521     521  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[01-01 11:16:19.237] (     3) [     6]     521     521  syncLegcyConfig
[01-01 11:16:19.239] (     4) [     6]     521     521  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[01-01 11:16:19.246] (     5) [     6]     521     521  setSubLog onOff:0 logType:lastlog
[01-01 11:16:19.246] (     6) [     6]     521     521  setSubLog onOff:0 logType:uboot
[01-01 11:16:19.247] (     7) [     6]     521     521  setSubLog onOff:0 logType:android
[01-01 11:16:19.247] (     8) [     6]     521     521  setSubLog onOff:0 logType:kernel
[01-01 11:16:19.247] (     9) [     6]     521     521  setSubLog onOff:0 logType:trace
[01-01 11:16:19.247] (    10) [     6]     521     521  setSubLog onOff:0 logType:sgm
[01-01 11:16:19.247] (    11) [     6]     521     521  setSubLog onOff:0 logType:sysinfo
[01-01 11:16:19.247] (    12) [     6]     521     521  setSubLog onOff:0 logType:thermal
[01-01 11:16:19.247] (    13) [     6]     521     521  setSubLog onOff:0 logType:ylogdebug
[01-01 11:16:19.247] (    14) [     6]     521     521  setSubLog onOff:0 logType:phoneinfo
[01-01 11:16:19.247] (    15) [     6]     521     521  setSubLog onOff:1 logType:hcidump
[01-01 11:16:19.247] (    16) [     6]     521     521  setSubLog onOff:1 logType:tcpdump
[01-01 11:16:19.247] (    17) [     6]     521     521  setSubLog onOff:0 logType:trustlog
[01-01 11:16:19.268] (     1) [     6]     634     641  LogReboot:startrebootServcie
[01-01 11:16:19.269] (     2) [     6]     634     634  aplogfilesize : 256
[01-01 11:16:19.271] (     3) [     6]     634     634  srootdir : default/
[01-01 11:16:19.272] (     4) [     6]     634     634  aplogmaxsize : 99%
[01-01 11:16:19.272] (     5) [     6]     634     634  aplogrotate : 1
[01-01 11:16:19.272] (     6) [     6]     634     634  prioritypath : 0
[01-01 11:16:24.275] (     7) [    11]     634     641  currentTime: 20250101111624-22544883
[01-01 11:16:24.275] (     8) [    11]     634     641  SystemBootMode::LINUXKERNEL
[01-01 11:16:24.276] (     9) [    11]     634     641  boot_cause: Pbint triggered
[01-01 11:16:24.276] (    10) [    11]     634     641  boot_reason: normalboot
[01-01 11:16:24.276] (    11) [    11]     634     641  boot_category: normalboot
[01-01 11:16:24.276] (    12) [    11]     634    1145  open /dev/block/by-name/sd_klog failed No such file or directory
[01-01 11:16:24.278] (    13) [    11]     634     641  Crash_reason: Normal
[08-01 07:19:26.909] (     1) [     5]     527     527  

.............................
[08-01 07:19:26.910] (     2) [     5]     527     527  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-01 07:19:26.915] (     3) [     5]     527     527  syncLegcyConfig
[08-01 07:19:26.916] (     4) [     5]     527     527  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-01 07:19:26.918] (     5) [     5]     527     527  setSubLog onOff:0 logType:lastlog
[08-01 07:19:26.918] (     6) [     5]     527     527  setSubLog onOff:0 logType:uboot
[08-01 07:19:26.918] (     7) [     5]     527     527  setSubLog onOff:0 logType:android
[08-01 07:19:26.918] (     8) [     5]     527     527  setSubLog onOff:0 logType:kernel
[08-01 07:19:26.918] (     9) [     5]     527     527  setSubLog onOff:0 logType:trace
[08-01 07:19:26.918] (    10) [     5]     527     527  setSubLog onOff:0 logType:sgm
[08-01 07:19:26.918] (    11) [     5]     527     527  setSubLog onOff:0 logType:sysinfo
[08-01 07:19:26.918] (    12) [     5]     527     527  setSubLog onOff:0 logType:thermal
[08-01 07:19:26.918] (    13) [     5]     527     527  setSubLog onOff:0 logType:ylogdebug
[08-01 07:19:26.918] (    14) [     5]     527     527  setSubLog onOff:0 logType:phoneinfo
[08-01 07:19:26.918] (    15) [     5]     527     527  setSubLog onOff:1 logType:hcidump
[08-01 07:19:26.918] (    16) [     5]     527     527  setSubLog onOff:1 logType:tcpdump
[08-01 07:19:26.918] (    17) [     5]     527     527  setSubLog onOff:0 logType:trustlog
[08-01 07:19:26.946] (     2) [     5]     643     643  aplogfilesize : 256
[08-01 07:19:26.945] (     2) [     5]     643     650  LogReboot:startrebootServcie
[08-01 07:19:26.946] (     3) [     5]     643     643  srootdir : default/
[08-01 07:19:26.946] (     4) [     5]     643     643  aplogmaxsize : 99%
[08-01 07:19:26.947] (     5) [     5]     643     643  aplogrotate : 1
[08-01 07:19:26.947] (     6) [     5]     643     643  prioritypath : 0
[08-01 07:19:31.954] (     7) [    10]     643     650  currentTime: 20250801071931-91369910
[08-01 07:19:31.955] (     8) [    10]     643     650  SystemBootMode::LINUXKERNEL
[08-01 07:19:31.956] (     9) [    10]     643     650  boot_cause: Pbint triggered
[08-01 07:19:31.956] (    10) [    10]     643     650  boot_reason: normalboot
[08-01 07:19:31.956] (    12) [    10]     643     650  boot_category: normalboot
[08-01 07:19:31.956] (    12) [    10]     643    1144  open /dev/block/by-name/sd_klog failed No such file or directory
[08-01 07:19:31.958] (    13) [    10]     643     650  Crash_reason: Normal
[08-01 09:58:18.937] (     1) [     5]     525     525  

.............................
[08-01 09:58:18.938] (     2) [     5]     525     525  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-01 09:58:18.944] (     3) [     5]     525     525  syncLegcyConfig
[08-01 09:58:18.950] (     4) [     5]     525     525  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-01 09:58:18.951] (     5) [     5]     525     525  setSubLog onOff:0 logType:lastlog
[08-01 09:58:18.951] (     6) [     5]     525     525  setSubLog onOff:0 logType:uboot
[08-01 09:58:18.951] (     7) [     5]     525     525  setSubLog onOff:0 logType:android
[08-01 09:58:18.951] (     8) [     5]     525     525  setSubLog onOff:0 logType:kernel
[08-01 09:58:18.951] (     9) [     5]     525     525  setSubLog onOff:0 logType:trace
[08-01 09:58:18.951] (    10) [     5]     525     525  setSubLog onOff:0 logType:sgm
[08-01 09:58:18.951] (    11) [     5]     525     525  setSubLog onOff:0 logType:sysinfo
[08-01 09:58:18.951] (    12) [     5]     525     525  setSubLog onOff:0 logType:thermal
[08-01 09:58:18.951] (    13) [     5]     525     525  setSubLog onOff:0 logType:ylogdebug
[08-01 09:58:18.951] (    14) [     5]     525     525  setSubLog onOff:0 logType:phoneinfo
[08-01 09:58:18.951] (    15) [     5]     525     525  setSubLog onOff:1 logType:hcidump
[08-01 09:58:18.951] (    16) [     5]     525     525  setSubLog onOff:1 logType:tcpdump
[08-01 09:58:18.951] (    17) [     5]     525     525  setSubLog onOff:0 logType:trustlog
[08-01 09:58:18.992] (     1) [     5]     647     654  LogReboot:startrebootServcie
[08-01 09:58:18.993] (     2) [     5]     647     647  aplogfilesize : 256
[08-01 09:58:18.994] (     3) [     5]     647     647  srootdir : default/
[08-01 09:58:18.994] (     4) [     5]     647     647  aplogmaxsize : 99%
[08-01 09:58:18.995] (     5) [     5]     647     647  aplogrotate : 1
[08-01 09:58:18.996] (     6) [     5]     647     647  prioritypath : 0
[08-01 09:58:23.997] (     7) [    10]     647     654  currentTime: 20250801095823-95632299
[08-01 09:58:23.998] (     8) [    10]     647     654  SystemBootMode::LINUXKERNEL
[08-01 09:58:23.998] (     9) [    10]     647     654  boot_cause: Pbint triggered
[08-01 09:58:23.998] (    10) [    10]     647     654  boot_reason: normalboot
[08-01 09:58:23.998] (    11) [    10]     647     654  boot_category: normalboot
[08-01 09:58:23.998] (    12) [    10]     647    1180  open /dev/block/by-name/sd_klog failed No such file or directory
[08-01 09:58:24.000] (    13) [    10]     647     654  Crash_reason: Normal
[08-01 11:23:37.871] (     1) [     5]     523     523  

.............................
[08-01 11:23:37.872] (     2) [     5]     523     523  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-01 11:23:37.876] (     3) [     5]     523     523  syncLegcyConfig
[08-01 11:23:37.895] (     4) [     5]     523     523  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-01 11:23:37.900] (     5) [     5]     523     523  setSubLog onOff:0 logType:lastlog
[08-01 11:23:37.900] (     6) [     5]     523     523  setSubLog onOff:0 logType:uboot
[08-01 11:23:37.900] (     7) [     5]     523     523  setSubLog onOff:0 logType:android
[08-01 11:23:37.900] (     8) [     5]     523     523  setSubLog onOff:0 logType:kernel
[08-01 11:23:37.900] (     9) [     5]     523     523  setSubLog onOff:0 logType:trace
[08-01 11:23:37.900] (    10) [     5]     523     523  setSubLog onOff:0 logType:sgm
[08-01 11:23:37.900] (    11) [     5]     523     523  setSubLog onOff:0 logType:sysinfo
[08-01 11:23:37.900] (    12) [     5]     523     523  setSubLog onOff:0 logType:thermal
[08-01 11:23:37.900] (    13) [     5]     523     523  setSubLog onOff:0 logType:ylogdebug
[08-01 11:23:37.901] (    14) [     5]     523     523  setSubLog onOff:0 logType:phoneinfo
[08-01 11:23:37.901] (    15) [     5]     523     523  setSubLog onOff:1 logType:hcidump
[08-01 11:23:37.901] (    16) [     5]     523     523  setSubLog onOff:1 logType:tcpdump
[08-01 11:23:37.901] (    17) [     5]     523     523  setSubLog onOff:0 logType:trustlog
[08-01 11:23:37.923] (     2) [     5]     636     641  LogReboot:startrebootServcie
[08-01 11:23:37.923] (     2) [     5]     636     636  aplogfilesize : 256
[08-01 11:23:37.924] (     3) [     5]     636     636  srootdir : default/
[08-01 11:23:37.924] (     4) [     5]     636     636  aplogmaxsize : 99%
[08-01 11:23:37.924] (     5) [     5]     636     636  aplogrotate : 1
[08-01 11:23:37.924] (     6) [     5]     636     636  prioritypath : 0
[08-01 11:23:42.941] (     7) [    10]     636     641  currentTime: 20250801112342-89075241
[08-01 11:23:42.941] (     8) [    10]     636     641  SystemBootMode::LINUXKERNEL
[08-01 11:23:42.942] (     9) [    10]     636     641  boot_cause: Reboot into normal
[08-01 11:23:42.942] (    10) [    10]     636     641  boot_reason: normalboot
[08-01 11:23:42.942] (    11) [    10]     636     641  boot_category: normalboot
[08-01 11:23:42.942] (    12) [    10]     636    1038  open /dev/block/by-name/sd_klog failed No such file or directory
[08-01 11:23:42.944] (    13) [    10]     636     641  Crash_reason: Normal
[08-01 12:01:52.152] (    18) [   877]     523     639  rcv clear cmd
[08-01 12:01:52.157] (    19) [   877]     523     639  mYlogClear:1
[08-01 12:01:52.161] (    20) [   877]     523     639  logSourceCnt:13 compressLevel:3
[08-01 12:01:52.167] (    14) [   877]     636     636  logfilewriter rcv clear cmd clear
[08-01 12:01:52.172] (    15) [   877]     636     636  clear rmdir:/cache/ylog/ap/history/
[08-01 12:01:52.172] (    16) [   877]     636     636  clear rmdir:/data/ylog/ap/history/
[08-01 12:01:52.172] (    17) [   877]     636     636  clear rmdir:/storage/sdcard0/ylog/ap/history/
[08-01 12:01:52.172] (    18) [   877]     636     636  clear rmdir:/storage/emulated/0/ylog/ap/history/
[08-01 12:01:52.173] (    19) [   877]     636     636  clear rmdir:/data/ylog/ap/history/
[08-01 12:01:52.173] (    20) [   877]     636     636  clear rmdir:/cache/ylog/last_ylog/
[08-01 12:01:52.173] (    21) [   877]     636     636  clear rmdir:/data/ylog/last_ylog/
[08-01 12:01:52.173] (    22) [   877]     636     636  clear rmdir:/storage/sdcard0/ylog/last_ylog/
[08-01 12:01:52.173] (    23) [   877]     636     636  clear rmdir:/storage/emulated/0/ylog/last_ylog/
[08-01 12:01:52.173] (    24) [   877]     636     636  clear rmdir:/data/ylog/last_ylog/
[08-01 12:01:52.173] (    25) [   877]     636     636  clear rmdir:/cache/ylog/SYSDUMP/
[08-01 12:01:52.173] (    26) [   877]     636     636  clear rmdir:/data/ylog/SYSDUMP/
[08-01 12:01:52.173] (    27) [   877]     636     636  clear rmdir:/storage/sdcard0/ylog/SYSDUMP/
[08-01 12:01:52.173] (    28) [   877]     636     636  clear rmdir:/storage/emulated/0/ylog/SYSDUMP/
[08-01 12:01:52.174] (    29) [   877]     636     636  clear rmdir:/data/ylog/SYSDUMP/
[08-01 12:01:52.174] (    30) [   877]     636     636  clear rmdir:/cache/ylog/ap/current/
[08-01 12:01:52.174] (    31) [   877]     636     636  clear rmdir:/data/ylog/ap/current/
[08-01 12:01:52.174] (    32) [   877]     636     636  clear rmdir:/storage/sdcard0/ylog/ap/current/
[08-01 12:01:52.174] (    33) [   877]     636     636  clear rmdir:/storage/emulated/0/ylog/ap/current/
[08-01 12:01:52.174] (    34) [   877]     636     636  clear rmdir:/data/ylog/ap/current/
[08-01 12:01:52.174] (    35) [   877]     636     636  clear rmdir:/cache//ylog/poweron/ap/
[08-01 12:01:52.174] (    36) [   877]     636     636  clear rmdir:/data//ylog/poweron/ap/
[08-01 12:01:52.174] (    37) [   877]     636     636  clear rmdir:/storage/sdcard0//ylog/poweron/ap/
[08-01 12:01:52.174] (    38) [   877]     636     636  clear rmdir:/storage/emulated/0//ylog/poweron/ap/
[08-01 12:01:52.175] (    39) [   877]     636     636  clear rmdir:/data//ylog/poweron/ap/
[08-01 12:01:52.175] (    40) [   877]     636     636  clear rmdir:/cache//slog/
[08-01 12:01:52.175] (    41) [   877]     636     636  clear rmdir:/data//slog/
[08-01 12:01:52.175] (    42) [   877]     636     636  clear rmdir:/storage/sdcard0//slog/
[08-01 12:01:52.175] (    43) [   877]     636     636  clear rmdir:/storage/emulated/0//slog/
[08-01 12:01:52.176] (    44) [   877]     636     636  clear rmdir:/data//slog/
[08-01 12:01:52.176] (    45) [   877]     636     636  clear rmdir:/cache//ylog/ap/SYSDUMP/
[08-01 12:01:52.176] (    46) [   877]     636     636  clear rmdir:/data//ylog/ap/SYSDUMP/
[08-01 12:01:52.176] (    47) [   877]     636     636  clear rmdir:/storage/sdcard0//ylog/ap/SYSDUMP/
[08-01 12:01:52.176] (    48) [   877]     636     636  clear rmdir:/storage/emulated/0//ylog/ap/SYSDUMP/
[08-01 12:01:52.176] (    49) [   877]     636     636  clear rmdir:/data//ylog/ap/SYSDUMP/
[08-01 12:01:52.176] (    50) [   877]     636     636  clear rmdir:/cache/ylog/ap/
[08-01 12:01:52.176] (    51) [   877]     636     636  clear rmdir:/data/ylog/ap/
[08-01 12:01:52.176] (    52) [   877]     636     636  clear rmdir:/storage/sdcard0/ylog/ap/
[08-01 12:01:52.176] (    53) [   877]     636     636  clear rmdir:/storage/emulated/0/ylog/ap/
[08-01 12:01:52.176] (    54) [   877]     636     636  clear rmdir:/data/ylog/ap/
[08-01 12:01:52.177] (    55) [   877]     636     636  clear rmfile:/cache/ylog/ylogtrace.info
[08-01 12:01:52.177] (    56) [   877]     636     636  clear rmfile:/cache/ylog/fwcrash.info
[08-01 12:01:52.177] (    57) [   877]     636     636  clear rmfile:/data/ylog/ylogtrace.info
[08-01 12:01:52.177] (    58) [   877]     636     636  clear rmfile:/data/ylog/fwcrash.info
[08-01 12:01:52.177] (    59) [   877]     636     636  clear rmfile:/storage/sdcard0/ylog/ylogtrace.info
[08-01 12:01:52.177] (    60) [   877]     636     636  clear rmfile:/storage/sdcard0/ylog/fwcrash.info
[08-01 12:01:52.177] (    61) [   877]     636     636  clear rmfile:/storage/emulated/0/ylog/ylogtrace.info
[08-01 12:01:52.177] (    62) [   877]     636     636  clear rmfile:/storage/emulated/0/ylog/fwcrash.info
[08-01 12:01:52.177] (    63) [   877]     636     636  clear rmfile:/data/ylog/ylogtrace.info
[08-01 12:01:52.177] (    64) [   877]     636     636  clear rmfile:/data/ylog/fwcrash.info
[08-01 12:01:52.179] (    65) [   877]     636     636  uniqueID file deleted(/data/ylog/loguid) ret=-1 No such file or directory
[08-01 12:01:52.179] (    66) [   877]     636     636  clear log take 0s
[08-01 12:01:53.107] (    21) [   878]     523     639  rcv enable cmd , cur enable=0
[08-01 12:01:53.108] (    22) [   878]     523     639  [status] set [enable]
[08-01 12:01:53.111] (    23) [   878]     523     639  cmd set  LogEnable=1
[08-01 12:01:53.112] (    24) [   878]     523     639  gYlogStatus:1
[08-01 12:01:53.112] (    25) [   878]     523     639  startlogServcie LogEnable : 1
[08-01 12:01:53.112] (    26) [   878]     523     639  __ERROR  write [READER_CMD_REMOVEALL enablecmd] error  [9(Bad file descriptor)] sendCmd
[08-01 12:01:53.113] (    27) [   878]     523     639  clear sublog params:13
[08-01 12:01:53.114] (    28) [   878]     523     639  setSubLog onOff:0 logType:lastlog
[08-01 12:01:53.114] (    29) [   878]     523     639  setSubLog onOff:0 logType:uboot
[08-01 12:01:53.114] (    30) [   878]     523     639  setSubLog onOff:0 logType:android
[08-01 12:01:53.114] (    31) [   878]     523     639  setSubLog onOff:0 logType:kernel
[08-01 12:01:53.114] (    32) [   878]     523     639  setSubLog onOff:0 logType:trace
[08-01 12:01:53.114] (    33) [   878]     523     639  setSubLog onOff:0 logType:sgm
[08-01 12:01:53.114] (    34) [   878]     523     639  setSubLog onOff:0 logType:sysinfo
[08-01 12:01:53.114] (    35) [   878]     523     639  setSubLog onOff:0 logType:thermal
[08-01 12:01:53.114] (    36) [   878]     523     639  setSubLog onOff:0 logType:ylogdebug
[08-01 12:01:53.114] (    37) [   878]     523     639  setSubLog onOff:0 logType:phoneinfo
[08-01 12:01:53.114] (    38) [   878]     523     639  setSubLog onOff:1 logType:hcidump
[08-01 12:01:53.114] (    39) [   878]     523     639  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[08-01 12:01:53.114] (    40) [   878]     523     639  setSubLog onOff:1 logType:tcpdump
[08-01 12:01:53.114] (    41) [   878]     523     639  setSubLog onOff:0 logType:trustlog
[08-01 12:01:53.121] (    42) [   878]     523     639  syncLegcyConfig
[08-01 12:01:53.121] (    43) [   878]     523     639  SubLog OnOff:0, logType lastlog is 0
[08-01 12:01:53.121] (    44) [   878]     523     639  SubLog OnOff:0, logType uboot is 0
[08-01 12:01:53.121] (    45) [   878]     523     639  SubLog OnOff:0, logType android is 0
[08-01 12:01:53.121] (    46) [   878]     523     639  SubLog OnOff:0, logType kernel is 0
[08-01 12:01:53.121] (    47) [   878]     523     639  SubLog OnOff:0, logType trace is 1
[08-01 12:01:53.121] (    48) [   878]     523     639  SubLog OnOff:0, logType sgm is 1
[08-01 12:01:53.121] (    49) [   878]     523     639  SubLog OnOff:0, logType sysinfo is 1
[08-01 12:01:53.121] (    50) [   878]     523     639  SubLog OnOff:0, logType thermal is 0
[08-01 12:01:53.121] (    51) [   878]     523     639  SubLog OnOff:0, logType ylogdebug is 1
[08-01 12:01:53.121] (    52) [   878]     523     639  SubLog OnOff:0, logType phoneinfo is 1
[08-01 12:01:53.121] (    53) [   878]     523     639  SubLog OnOff:1, logType hcidump is 0
[08-01 12:01:53.121] (    54) [   878]     523     639  SubLog OnOff:1, logType tcpdump is 0
[08-01 12:01:53.121] (    55) [   878]     523     639  SubLog OnOff:0, logType trustlog is 1
[08-01 12:01:53.121] (    56) [   878]     523     639  index:11,logType:tcpdump, logSize:256, totalSize:4096
[08-01 12:01:53.121] (    57) [   878]     523     639  index:10,logType:hcidump, logSize:64, totalSize:1024
[08-01 12:01:53.121] (    58) [   878]     523     639  value:default
[08-01 12:01:53.121] (    59) [   878]     523     639  make dir:/data/ylog/ap/
[08-01 12:01:53.121] (    60) [   878]     523     639  mSubLog:1
[08-01 12:01:53.122] (    61) [   878]     523     639  mSubLog:1
[08-01 12:01:53.128] (    62) [   878]     523    8030  [lastlog]  configure is [0]
[08-01 12:01:53.128] (    63) [   878]     523    8030  [uboot]  configure is [0]
[08-01 12:01:53.128] (    64) [   878]     523    8030  [android]  configure is [0]
[08-01 12:01:53.128] (    65) [   878]     523    8030  [kernel]  configure is [0]
[08-01 12:01:53.128] (    66) [   878]     523    8030  [trace]  configure is [1]
[08-01 12:01:53.130] (    67) [   878]     523    8030  [sgm]  configure is [1]
[08-01 12:01:53.132] (    68) [   878]     523    8030  [sysinfo]  configure is [1]
[08-01 12:01:53.134] (    69) [   878]     523    8030  [thermal]  configure is [0]
[08-01 12:01:53.134] (    70) [   878]     523    8030  [ylogdebug]  configure is [1]
[08-01 12:01:53.136] (    71) [   878]     523    8030  [phoneinfo]  configure is [1]
[08-01 12:01:53.152] (    72) [   878]     523    8030  [hcidump]  configure is [0]
[08-01 12:01:53.152] (    73) [   878]     523    8030  [tcpdump]  configure is [0]
[08-01 12:01:53.152] (    74) [   878]     523    8030  [trustlog]  configure is [1]
[08-01 12:01:53.155] (    75) [   878]     523    8030  listen to 6 source 
[08-01 12:01:53.168] (    76) [   878]     523    8030  ListenLogSource setValue:uboot
[08-01 12:01:53.168] (    77) [   878]     523    8030  [uboot] set [1]
[08-01 12:01:53.179] (    78) [   878]     523    8030  ListenLogSource setValue:lastlog
[08-01 12:01:53.179] (    79) [   878]     523    8030  [lastlog] set [1]
[08-01 12:01:53.197] (    80) [   878]     523    8030  ListenLogSource setValue:kernel
[08-01 12:01:53.197] (    81) [   878]     523    8030  [kernel] set [1]
[08-01 12:01:53.219] (    82) [   878]     523    8030  ListenLogSource setValue:android
[08-01 12:01:53.219] (    83) [   878]     523    8030  [android] set [1]
[08-01 12:01:53.224] (    84) [   878]     523    8030  ListenLogSource setValue:hcidump
[08-01 12:01:53.225] (    85) [   878]     523    8030  [hcidump] set [1]
[08-01 12:01:53.233] (    67) [   878]     636     636  mount changed: [] -> [/data/]
[08-01 12:01:53.246] (    68) [   878]     636     636  set logdir to /data/ylog/ap/, diskfree:219220
[08-01 12:01:53.254] (    69) [   878]     636     636  last ylog file  [] not exsit,backup mmap file
[08-01 12:01:53.254] (    70) [   878]     636     636  last ylog not exit, backupMmapData error.
[08-01 12:01:53.255] (    71) [   878]     636     636  create first file
[08-01 12:01:53.256] (    72) [   878]     636     636  get new  file name(new logfile) : /data/ylog/ap/000-0801_120153_poweron.ylog 
[08-01 12:01:53.257] (    73) [   878]     636     636  open log file:/data/ylog/ap/000-0801_120153_poweron.ylog fd:19 diskfree:219220
[08-01 12:01:53.259] (    74) [   878]     636     636  update UID file:/data/ylog/loguid=0
[08-01 12:01:53.264] (    86) [   878]     523     630  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[08-01 12:01:53.270] (    87) [   878]     523    8030  ListenLogSource setValue:tcpdump
[08-01 12:01:53.270] (    88) [   878]     523    8030  [tcpdump] set [1]
[08-01 12:01:53.281] (    89) [   878]     523    8030  __ERROR  [audiodump]close source  failed<cmd> error  [2(No such file or directory)] UnListenLogSource
[08-01 12:01:53.282] (    90) [   878]     523     639  [tcpdump]bin file changed2 tcpdump -i any -p   -U -w - -s 0 -s 3000 -s 3000 by -s 3000
[08-01 12:01:53.282] (    91) [   878]     523     639  [tcpdump_c] set [-s 3000]
[08-01 12:01:53.298] (    92) [   878]     523    8030  index:11 log buffer is null 
[08-01 12:01:53.299] (    93) [   878]     523    8030  index:12 log buffer is null 
[08-01 12:01:53.559] (    75) [   878]     636    8063  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[08-01 12:01:53.559] (    76) [   878]     636    8063  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
run finished on 08-01 12:01:54
ylogdebug end




