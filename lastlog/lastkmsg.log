

userrequested
servicemanager' requested start, but it is already running (flags: 2052)
<14>[ 1552.125015][ T1@C6] init: Stopping 225 services by sending SIGTERM
<14>[ 1552.125088][ T1@C6] init: Sending signal 15 to service 'bootanim' (pid 12414) process group...
<12>[ 1552.125603][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1003/pid_12414 after 0 ms
<11>[ 1552.125653][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1003/pid_12414: Device or resource busy
<14>[ 1552.126650][ T1@C7] init: Sending signal 15 to service 'wpa_supplicant' (pid 1576) process group...
<12>[ 1552.126979][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_1576 after 0 ms
<11>[ 1552.127025][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_1576: Device or resource busy
<14>[ 1552.127475][ T1@C7] init: Sending signal 15 to service 'srmi_proxyd' (pid 1218) process group...
<12>[ 1552.127833][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_1218 after 0 ms
<11>[ 1552.127880][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_1218: Device or resource busy
<14>[ 1552.128323][ T1@C7] init: Sending signal 15 to service 'vendor.cp_diskserver' (pid 985) process group...
<12>[ 1552.128598][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_985 after 0 ms
<11>[ 1552.128638][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_985: Device or resource busy
<14>[ 1552.137612][ T1@C7] init: Sending signal 15 to service 'vendor.wcn_chr' (pid 799) process group...
<12>[ 1552.138044][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_12345/pid_799 after 0 ms
<11>[ 1552.138090][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_12345/pid_799: Device or resource busy
<14>[ 1552.138532][ T1@C7] init: Sending signal 15 to service 'vendor.prodproxy' (pid 795) process group...
<12>[ 1552.138782][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_795 after 0 ms
<11>[ 1552.138827][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_795: Device or resource busy
<14>[ 1552.139204][ T1@C7] init: Sending signal 15 to service 'vendor.wcnd' (pid 793) process group...
<12>[ 1552.139476][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_793 after 0 ms
<11>[ 1552.139513][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_793: Device or resource busy
<14>[ 1552.140101][ T1@C7] init: Sending signal 15 to service 'update_engine' (pid 791) process group...
<12>[ 1552.140368][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_791 after 0 ms
<11>[ 1552.140405][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_791: Device or resource busy
<14>[ 1552.140761][ T1@C7] init: Sending signal 15 to service 'gatekeeperd' (pid 790) process group...
<12>[ 1552.140993][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_790 after 0 ms
<11>[ 1552.141029][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_790: Device or resource busy
<14>[ 1552.141351][ T1@C7] init: Sending signal 15 to service 'media.swcodec' (pid 787) process group...
<12>[ 1552.142092][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1046/pid_787 after 0 ms
<11>[ 1552.142137][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1046/pid_787: Device or resource busy
<14>[ 1552.142546][ T1@C6] init: Sending signal 15 to service 'tool_service' (pid 784) process group...
<12>[ 1552.142794][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_784 after 0 ms
<11>[ 1552.142833][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_784: Device or resource busy
<6>[ 1552.143037][T438@C2] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1552.143461][ T1@C7] init: Sending signal 15 to service 'ext_data' (pid 769) process group...
<12>[ 1552.143959][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_769 after 0 ms
<11>[ 1552.144006][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_769: Device or resource busy
<14>[ 1552.144419][ T1@C7] init: Sending signal 15 to service 'unionpnp_service' (pid 768) process group...
<12>[ 1552.144675][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_768 after 0 ms
<11>[ 1552.144719][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_768: Device or resource busy
<14>[ 1552.145141][ T1@C7] init: Sending signal 15 to service 'vendor.thermald' (pid 767) process group...
<12>[ 1552.145627][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_767 after 0 ms
<11>[ 1552.145681][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_767: Device or resource busy
<14>[ 1552.146145][ T1@C7] init: Sending signal 15 to service 'vendor.srtd' (pid 761) process group...
<12>[ 1552.146506][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_761 after 0 ms
<11>[ 1552.146559][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_761: Device or resource busy
<14>[ 1552.146949][ T1@C7] init: Sending signal 15 to service 'vendor.refnotify' (pid 758) process group...
<12>[ 1552.147324][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_758 after 0 ms
<11>[ 1552.147380][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_758: Device or resource busy
<14>[ 1552.147800][ T1@C7] init: Sending signal 15 to service 'phasecheckserver' (pid 754) process group...
<12>[ 1552.148132][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_754 after 0 ms
<11>[ 1552.148185][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_754: Device or resource busy
<14>[ 1552.148698][ T1@C7] init: Sending signal 15 to service 'uniber' (pid 747) process group...
<12>[ 1552.148986][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_747 after 0 ms
<11>[ 1552.149038][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_747: Device or resource busy
<14>[ 1552.158455][ T1@C7] init: Sending signal 15 to service 'vendor.charged' (pid 743) process group...
<12>[ 1552.158867][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_743 after 0 ms
<11>[ 1552.158926][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_743: Device or resource busy
<14>[ 1552.159336][ T1@C7] init: Sending signal 15 to service 'vendor.media.omx' (pid 736) process group...
<12>[ 1552.159789][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1046/pid_736 after 0 ms
<11>[ 1552.159837][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1046/pid_736: Device or resource busy
<14>[ 1552.160517][ T1@C6] init: Sending signal 15 to service 'uniresctlopt' (pid 735) process group...
<12>[ 1552.160850][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_735 after 0 ms
<11>[ 1552.160892][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_735: Device or resource busy
<14>[ 1552.161272][ T1@C6] init: Sending signal 15 to service 'slogmodem' (pid 734) process group...
<12>[ 1552.161669][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_734 after 0 ms
<11>[ 1552.161721][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_734: Device or resource busy
<14>[ 1552.184879][ T1@C7] init: Sending signal 15 to service 'remotedisplay' (pid 733) process group...
<12>[ 1552.185424][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1013/pid_733 after 0 ms
<11>[ 1552.185481][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1013/pid_733: Device or resource busy
<14>[ 1552.220636][ T1@C7] init: Sending signal 15 to service 'linkturbonative' (pid 732) process group...
<12>[ 1552.221100][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_732 after 0 ms
<11>[ 1552.221149][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_732: Device or resource busy
<14>[ 1552.254214][ T1@C2] init: Sending signal 15 to service 'ims_bridged' (pid 731) process group...
<12>[ 1552.254804][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_731 after 0 ms
<11>[ 1552.254875][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_731: Device or resource busy
<6>[ 1552.272139][T387@C0] WCN BASE: 
<6>[ 1552.272177][T387@C0] WCN BASE: parse_event: wcn_chr_disable
<6>[ 1552.272837][T387@C0] WCN BASE: wait for chr server ready
<14>[ 1552.290918][ T1@C7] init: Sending signal 15 to service 'wificond' (pid 730) process group...
<12>[ 1552.291383][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1010/pid_730 after 0 ms
<11>[ 1552.291429][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1010/pid_730: Device or resource busy
<3>[ 1552.316504][T520@C3] sprd-wlan: sprd_chr_client_thread, CHR: kernel_recvmsg faild, go to exit
<14>[ 1552.325693][ T1@C2] init: Sending signal 15 to service 'storaged' (pid 728) process group...
<12>[ 1552.326517][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_728 after 0 ms
<11>[ 1552.326604][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_728: Device or resource busy
<6>[ 1552.341106][T662@C0] [drm] sprd_panel_unprepare()
<6>[ 1552.356318][T662@C3] [drm] sprd_dpu_atomic_disable()
<14>[ 1552.365627][ T1@C2] init: Sending signal 15 to service 'media' (pid 726) process group...
<12>[ 1552.366411][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1013/pid_726 after 0 ms
<11>[ 1552.366479][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1013/pid_726: Device or resource busy
<14>[ 1552.391211][ T1@C2] init: Sending signal 15 to service 'mediametrics' (pid 725) process group...
<12>[ 1552.392059][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1013/pid_725 after 0 ms
<11>[ 1552.392145][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1013/pid_725: Device or resource busy
<14>[ 1552.392802][ T1@C2] init: Sending signal 15 to service 'mediaextractor' (pid 724) process group...
<12>[ 1552.393465][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1040/pid_724 after 0 ms
<11>[ 1552.393542][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1040/pid_724: Device or resource busy
<14>[ 1552.394173][ T1@C2] init: Sending signal 15 to service 'installd' (pid 723) process group...
<12>[ 1552.394870][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_723 after 0 ms
<11>[ 1552.394939][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_723: Device or resource busy
<14>[ 1552.395423][ T1@C2] init: Sending signal 15 to service 'incidentd' (pid 720) process group...
<12>[ 1552.395808][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1067/pid_720 after 0 ms
<11>[ 1552.395867][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1067/pid_720: Device or resource busy
<14>[ 1552.396380][ T1@C2] init: Sending signal 15 to service 'cameraserver' (pid 718) process group...
<12>[ 1552.397466][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1047/pid_718 after 0 ms
<11>[ 1552.397558][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1047/pid_718: Device or resource busy
<6>[ 1552.397644][T1029@C5] [ASoC: PCM ] sprd_pcm_trigger, FE_DAI_ID_FAST_P cpu_dai->id = 3 Trigger Playback cmd:0
<6>[ 1552.397656][T1029@C5] [ASoC: PCM ] pcm Stop
<6>[ 1552.397664][T1029@C5] [ASoC: PCM ] pcm E
<6>[ 1552.397679][T1029@C5] [ASoC: VBC ] scene_fast_trigger: 9063:scene_fast_trigger dai:BE_DAI_ID_FAST_P_CODEC(3) scene:VBC_DAI_ID_FAST_P playback, cmd=0
<14>[ 1552.398200][ T1@C2] init: Sending signal 15 to service 'uniview' (pid 716) process group...
<6>[ 1552.398439][T1029@C5] [ASoC: PCM ] sprd_pcm_hw_free, FE_DAI_ID_FAST_P cpu_dai->id = 3 Playback
<6>[ 1552.398449][T1029@C5] [ASoC: PCM ] sprd_pcm_hw_free release id=2
<6>[ 1552.398568][T1029@C5] [ASoC: FE.VBC ] fe_hw_free fe dai: FE_DAI_ID_FAST_P(3) playback
<12>[ 1552.398599][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_716 after 0 ms
<11>[ 1552.398655][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_716: Device or resource busy
<14>[ 1552.399166][ T1@C2] init: Sending signal 15 to service 'vendor.teensproxy' (pid 715) process group...
<6>[ 1552.399301][T1029@C5] [ASoC: VBC ] scene_fast_hw_free: 9037:scene_fast_hw_free dai:BE_DAI_ID_FAST_P_CODEC(3) scene:VBC_DAI_ID_FAST_P playback
<12>[ 1552.399434][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_715 after 0 ms
<11>[ 1552.399491][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_715: Device or resource busy
<6>[ 1552.406718][T1029@C1] [ASoC: VBC ] scene_fast_shutdown: 8954:scene_fast_shutdown dai:BE_DAI_ID_FAST_P_CODEC(3) scene:VBC_DAI_ID_FAST_P playback
<6>[ 1552.406759][T1029@C1] [ASoC: VBC ] get_startup_scene_dac_id: 6719:get_startup_scene_dac_id scene is VBC_DAI_ID_FAST_P(id 3) dac_id = 0
<6>[ 1552.406768][T1029@C1] [ASoC: VBC ] get_startup_scene_adc_id: 6808:get_startup_scene_adc_id scene is VBC_DAI_ID_FAST_P(id 3) adc_id = 0
<6>[ 1552.406779][T1029@C1] [Audio:SIPC] audio_cmd_copy, write cmd para: txbuf_addr_v=0xffffffc00c18d000, tx_len=620
<6>[ 1552.406791][T1029@C1] [Audio:SMSG] warning aud_smsg_recv cache is empty!
<4>[ 1552.406796][T1029@C1] [Audio:SIPC] aud_recv_cmd channel =0 cmd=5 ENODATA
<6>[ 1552.406801][T1029@C1] [Audio:SIPC] aud_send_cmd in,cmd =5 id:3 ret-value:0
<6>[ 1552.406805][T1029@C1] [Audio:SIPC] aud_send_msg,cmd: 0x5, value0: 0x3, value1: 0x0,
<6>[ 1552.406811][T1029@C1] [Audio:SIPC]  value2: 0x94380000, value3: 0x0
<6>[ 1552.406815][T1029@C1] [Audio:SMSG] aud_smsg_send: dst=1, channel=0
<6>[ 1552.406848][T1029@C1] [Audio:SIPC] aud_send_cmd 688 wait dsp
<6>[ 1552.407357][T1029@C1] [Audio:SIPC] aud_recv_cmd, chan: 0x0, cmd: 0x5, value0: 0x3, value1: 0x0,
<6>[ 1552.407373][T1029@C1] [Audio:SIPC]  value2: 0x94380000, value3: 0x0, timeout: 750
<6>[ 1552.407381][T1029@C1] [Audio:SIPC] aud_send_cmd out,cmd =5 id:3 ret-value:0,repeat_count=1
<3>[ 1552.407396][T1029@C1] [ASoC:HDST2730] sprd_enable_hmicbias_polling no headset insert!
<6>[ 1552.407424][T1029@C1] [ASoC: PCM ] sprd_pcm_close FE_DAI_ID_FAST_P Close Playback
<6>[ 1552.417046][T1029@C3] [ASoC:BOARD] hook_general_spk  0
<6>[ 1552.417074][T1029@C3] [ASoC:BOARD] hook_general_spk id: 0, gpio: 193, mode: 3, on: 0
<14>[ 1552.440868][ T1@C4] init: Sending signal 15 to service 'nfc_hal_service.tms.aidl' (pid 714) process group...
<12>[ 1552.441542][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1027/pid_714 after 0 ms
<11>[ 1552.441623][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1027/pid_714: Device or resource busy
<6>[ 1552.480871][T1029@C3] [ASoC:BOARD] hook_general_spk id: 0, gpio: 193, mode: 3, on: 0, audio_sense:0
<6>[ 1552.480923][T1029@C3] [ASoC:BOARD] hook_general_ctl hook state {0, 1, 0}
<6>[ 1552.480939][T1029@C3] [ASoC:BOARD] hook_general_spk  0
<6>[ 1552.480944][T1029@C3] [ASoC:BOARD] hook_general_spk id: 1, gpio: 187, mode: 1, on: 0
<14>[ 1552.485542][ T1@C6] init: Sending signal 15 to service 'vendor.identity-default' (pid 713) process group...
<12>[ 1552.485872][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_713 after 0 ms
<11>[ 1552.485910][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_713: Device or resource busy
<14>[ 1552.486318][ T1@C6] init: Sending signal 15 to service 'vendor.face-default' (pid 712) process group...
<12>[ 1552.486533][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_712 after 0 ms
<11>[ 1552.486566][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_712: Device or resource busy
<14>[ 1552.487096][ T1@C6] init: Sending signal 15 to service 'traced' (pid 706) process group...
<12>[ 1552.487334][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_9999/pid_706 after 0 ms
<11>[ 1552.487367][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_9999/pid_706: Device or resource busy
<14>[ 1552.487765][ T1@C6] init: Sending signal 15 to service 'traced_probes' (pid 705) process group...
<12>[ 1552.488034][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_9999/pid_705 after 0 ms
<11>[ 1552.488070][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_9999/pid_705: Device or resource busy
<14>[ 1552.489063][ T1@C6] init: Sending signal 15 to service 'drm' (pid 704) process group...
<12>[ 1552.489535][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1019/pid_704 after 0 ms
<11>[ 1552.489576][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1019/pid_704: Device or resource busy
<14>[ 1552.495931][ T1@C4] init: Sending signal 15 to service 'vendor.ril-daemon' (pid 614) process group...
<12>[ 1552.496899][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1001/pid_614 after 0 ms
<11>[ 1552.496980][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1001/pid_614: Device or resource busy
<14>[ 1552.499641][ T1@C4] init: Sending signal 15 to service 'vendor.tsupplicant' (pid 613) process group...
<12>[ 1552.500205][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_613 after 0 ms
<11>[ 1552.500286][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_613: Device or resource busy
<14>[ 1552.500804][ T1@C4] init: Sending signal 15 to service 'vendor.tee_rpc' (pid 612) process group...
<12>[ 1552.501117][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_612 after 0 ms
<11>[ 1552.501178][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_612: Device or resource busy
<6>[ 1552.527287][T813@C1] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1552.527331][T813@C1] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1552.527372][T813@C1] [Audio:DSPDUMP] audio_dsp_release
<14>[ 1552.533168][ T1@C4] init: Sending signal 15 to service 'vendor.nsproxy' (pid 611) process group...
<12>[ 1552.533972][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_611 after 0 ms
<11>[ 1552.534071][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_611: Device or resource busy
<14>[ 1552.534807][ T1@C4] init: Sending signal 15 to service 'vendor.rpmbproxy' (pid 609) process group...
<6>[ 1552.557481][T1029@C5] [ASoC:BOARD] hook_general_spk id: 1, gpio: 187, mode: 1, on: 0, audio_sense:0
<6>[ 1552.557527][T1029@C5] [ASoC:BOARD] hook_general_ctl hook state {0, 0, 0}
<12>[ 1552.571491][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_609 after 0 ms
<11>[ 1552.571624][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_609: Device or resource busy
<14>[ 1552.572550][ T1@C4] init: Sending signal 15 to service 'vendor.modem_control' (pid 608) process group...
<12>[ 1552.573770][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_608 after 0 ms
<11>[ 1552.573861][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_608: Device or resource busy
<14>[ 1552.574580][ T1@C4] init: Sending signal 15 to service 'gpsd' (pid 607) process group...
<12>[ 1552.575835][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_607 after 0 ms
<11>[ 1552.576009][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_607: Device or resource busy
<14>[ 1552.576704][ T1@C4] init: Sending signal 15 to service 'vendor.engpcclientlte' (pid 606) process group...
<12>[ 1552.577604][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_606 after 0 ms
<11>[ 1552.577680][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_606: Device or resource busy
<14>[ 1552.578291][ T1@C4] init: Sending signal 15 to service 'audio_tunning_service' (pid 604) process group...
<12>[ 1552.578721][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1041/pid_604 after 0 ms
<11>[ 1552.578782][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1041/pid_604: Device or resource busy
<14>[ 1552.579219][ T1@C4] init: Sending signal 15 to service 'audio_parameter_parser_service' (pid 603) process group...
<12>[ 1552.579547][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1041/pid_603 after 0 ms
<11>[ 1552.579602][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1041/pid_603: Device or resource busy
<14>[ 1552.580012][ T1@C4] init: Sending signal 15 to service 'surfaceflinger' (pid 602) process group...
<12>[ 1552.582361][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_602 after 0 ms
<11>[ 1552.582450][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_602: Device or resource busy
<14>[ 1552.583097][ T1@C4] init: Sending signal 15 to service 'gpu' (pid 594) process group...
<12>[ 1552.584017][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1072/pid_594 after 0 ms
<11>[ 1552.584097][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1072/pid_594: Device or resource busy
<14>[ 1552.584749][ T1@C4] init: Sending signal 15 to service 'credstore' (pid 591) process group...
<12>[ 1552.586493][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1076/pid_591 after 1 ms
<11>[ 1552.586572][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1076/pid_591: Device or resource busy
<14>[ 1552.587174][ T1@C4] init: Sending signal 15 to service 'audioserver' (pid 590) process group...
<12>[ 1552.587872][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1041/pid_590 after 0 ms
<11>[ 1552.587933][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1041/pid_590: Device or resource busy
<14>[ 1552.597799][ T1@C5] init: Sending signal 15 to service 'vendor.sprd.hardware.vibrator-service' (pid 589) process group...
<12>[ 1552.598522][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_589 after 0 ms
<11>[ 1552.598611][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_589: Device or resource busy
<14>[ 1552.733902][ T1@C7] init: Sending signal 15 to service 'vendor.tool-default' (pid 583) process group...
<12>[ 1552.734386][ T1@C7] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_583 after 0 ms
<11>[ 1552.734431][ T1@C7] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_583: Device or resource busy
<14>[ 1552.762304][ T1@C4] init: Sending signal 15 to service 'vendor.thermal-hal-2-0' (pid 582) process group...
<12>[ 1552.762860][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_582 after 0 ms
<11>[ 1552.762929][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_582: Device or resource busy
<14>[ 1552.772689][ T1@C3] init: Sending signal 15 to service 'miscdata_hal_service' (pid 581) process group...
<12>[ 1552.773727][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_581 after 0 ms
<11>[ 1552.773832][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_581: Device or resource busy
<14>[ 1552.774760][ T1@C3] init: Sending signal 15 to service 'vendor.sprd.hardware.connmgr@1.0-service' (pid 580) process group...
<12>[ 1552.775218][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_580 after 0 ms
<11>[ 1552.775291][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_580: Device or resource busy
<14>[ 1552.775831][ T1@C3] init: Sending signal 15 to service 'vendor.sprd.broadcastradio-hal2' (pid 579) process group...
<12>[ 1552.776223][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1041/pid_579 after 0 ms
<11>[ 1552.776282][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1041/pid_579: Device or resource busy
<6>[ 1552.789357][T1029@C2] [ASoC:SC2730] dalr_dc_os_event Dpop sucessed! i=1, ANA_STS1=0x7a80
<14>[ 1552.809051][ T1@C5] init: Sending signal 15 to service 'vendor.sprd.hardware.gnss-service' (pid 578) process group...
<12>[ 1552.810227][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_578 after 0 ms
<11>[ 1552.810320][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_578: Device or resource busy
<14>[ 1552.815024][ T1@C5] init: Sending signal 15 to service 'vendor.focaltech.fps_hal' (pid 577) process group...
<12>[ 1552.815779][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_577 after 0 ms
<11>[ 1552.815869][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_577: Device or resource busy
<14>[ 1552.816735][ T1@C5] init: Sending signal 15 to service 'vendor.power-default' (pid 576) process group...
<12>[ 1552.817180][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_576 after 0 ms
<11>[ 1552.817247][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_576: Device or resource busy
<14>[ 1552.817835][ T1@C5] init: Sending signal 15 to service 'vendor.oemlock-default' (pid 575) process group...
<12>[ 1552.818180][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_575 after 0 ms
<11>[ 1552.818239][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_575: Device or resource busy
<14>[ 1552.818625][ T1@C5] init: Sending signal 15 to service 'vendor.sprd.hardware.trusty-service' (pid 574) process group...
<12>[ 1552.818931][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_574 after 0 ms
<11>[ 1552.818985][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_574: Device or resource busy
<14>[ 1552.819362][ T1@C5] init: Sending signal 15 to service 'vendor.rebootescrow-default' (pid 573) process group...
<12>[ 1552.819700][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_573 after 0 ms
<11>[ 1552.819756][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_573: Device or resource busy
<14>[ 1552.820403][ T1@C5] init: Sending signal 15 to service 'vendor.power.stats-default' (pid 572) process group...
<6>[ 1552.821079][T1029@C2] [ASoC:SC2730] DAC Off
<6>[ 1552.821095][T1029@C2] [ASoC:SC2730] sprd_codec_sample_rate_setting AD 48000 DA 48000 AD1 48000
<6>[ 1552.821114][T1029@C2] [ASoC:SC2730] DAC Off
<6>[ 1552.821118][T1029@C2] [ASoC:SC2730] sprd_codec_sample_rate_setting AD 48000 DA 48000 AD1 48000
<6>[ 1552.821272][T1029@C2] [ASoC:SC2730] sprd_codec_power_disable, line: 1420
<6>[ 1552.826042][T1029@C2] [ASoC: VBC ] vbc_put_iis_tx_lr_mod_sel: 2579:vbc_put_iis_tx_lr_mod_sel, VBC_MUX_IIS_TX_DAC0 = LEFT_HIGH
<6>[ 1552.826083][T1029@C2] [Audio:SIPC] audio_cmd_copy, write cmd para: txbuf_addr_v=0xffffffc00c18d000, tx_len=8
<6>[ 1552.826095][T1029@C2] [Audio:SMSG] warning aud_smsg_recv cache is empty!
<4>[ 1552.826100][T1029@C2] [Audio:SIPC] aud_recv_cmd channel =0 cmd=9 ENODATA
<6>[ 1552.826106][T1029@C2] [Audio:SIPC] aud_send_cmd in,cmd =9 id:30 ret-value:0
<6>[ 1552.826110][T1029@C2] [Audio:SIPC] aud_send_msg,cmd: 0x9, value0: 0x1e, value1: 0xffffffff,
<6>[ 1552.826117][T1029@C2] [Audio:SIPC]  value2: 0x94380000, value3: 0x0
<6>[ 1552.826120][T1029@C2] [Audio:SMSG] aud_smsg_send: dst=1, channel=0
<6>[ 1552.826148][T1029@C2] [Audio:SIPC] aud_send_cmd 688 wait dsp
<12>[ 1552.845246][ T1@C1] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_572 after 0 ms
<11>[ 1552.845400][ T1@C1] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_572: Device or resource busy
<14>[ 1552.846072][ T1@C1] init: Sending signal 15 to service 'vendor.network-default' (pid 570) process group...
<12>[ 1552.846563][ T1@C1] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_570 after 0 ms
<11>[ 1552.846622][ T1@C1] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_570: Device or resource busy
<6>[ 1552.853046][T1029@C2] [Audio:SIPC] aud_recv_cmd, chan: 0x0, cmd: 0x9, value0: 0x1e, value1: 0xffffffff,
<6>[ 1552.853075][T1029@C2] [Audio:SIPC]  value2: 0x94380000, value3: 0x0, timeout: 750
<6>[ 1552.853083][T1029@C2] [Audio:SIPC] aud_send_cmd out,cmd =9 id:30 ret-value:0,repeat_count=1
<6>[ 1552.853440][T1029@C2] [ASoC:BOARD] Ext Spk1 Switch Off
<6>[ 1552.854050][T1029@C2] [ASoC:BOARD] Ext Spk Switch Off
<6>[ 1552.854441][T1029@C2] [ASoC:SC2730] dac switch 4,shift=3,set=0
<6>[ 1552.854682][T1029@C2] [ASoC:SC2730] dac switch 0,shift=2,set=0
<6>[ 1552.855324][T1029@C2] [Audio:PIPE] aud_pipe_write:aud_pipe_data->channel:2,count:20
<6>[ 1552.855343][T1029@C2] [Audio:PIPE] user_msg_in.channel=0x2
<6>[ 1552.855348][T1029@C2] [Audio:PIPE] user_msg_in.command=0x34
<6>[ 1552.855352][T1029@C2] [Audio:PIPE] user_msg_in.parameter0=0x5d
<6>[ 1552.855356][T1029@C2] [Audio:PIPE] user_msg_in.parameter1=0x0
<6>[ 1552.855359][T1029@C2] [Audio:PIPE] user_msg_in.parameter2=0x0
<6>[ 1552.855363][T1029@C2] [Audio:PIPE] user_msg_in.parameter3=0x0
<6>[ 1552.855367][T1029@C2] [Audio:SIPC] aud_send_cmd_no_wait no wait
<6>[ 1552.855370][T1029@C2] [Audio:SIPC] aud_send_msg,cmd: 0x34, value0: 0x5d, value1: 0x0,
<6>[ 1552.855375][T1029@C2] [Audio:SIPC]  value2: 0x0, value3: 0x0
<6>[ 1552.855380][T1029@C2] [Audio:SMSG] aud_smsg_send: dst=1, channel=2
<6>[ 1552.855461][T1029@C2] [ASoC: VBC ] vbc_put_agdsp_aud_access: 4243:vbc_put_agdsp_aud_access agcp_access_aud_cnt = 1, agcp_access_a2dp_cnt = 0
<6>[ 1552.855469][T1029@C2] [ASoC: VBC ] vbc_put_agdsp_aud_access: 4259:audio hal agdsp_access_disable
<6>[ 1552.855759][T12080@C2] [Audio:agdsp-pd]: sprd_agdsp_pw_off,dsp_ac->state->ap_enable_cnt=0,dsp_ac->state->cp_enable_cnt=0.
<6>[ 1552.855872][T1029@C2] [ASoC: VBC ] vbc_put_agdsp_aud_access: 4243:vbc_put_agdsp_aud_access agcp_access_aud_cnt = 0, agcp_access_a2dp_cnt = 0
<6>[ 1552.856146][T1029@C2] [Audio:agdsp-pd]: sprd_agdsp_pw_on, ap_enable_cnt=1, cp_enable_cnt=0.cnt=1
<6>[ 1552.856196][T1029@C2] [Audio:PIPE] aud_pipe_write:aud_pipe_data->channel:2,count:20
<6>[ 1552.856205][T1029@C2] [Audio:PIPE] user_msg_in.channel=0x2
<6>[ 1552.856208][T1029@C2] [Audio:PIPE] user_msg_in.command=0x34
<6>[ 1552.856211][T1029@C2] [Audio:PIPE] user_msg_in.parameter0=0x5e
<6>[ 1552.856214][T1029@C2] [Audio:PIPE] user_msg_in.parameter1=0x0
<6>[ 1552.856217][T1029@C2] [Audio:PIPE] user_msg_in.parameter2=0x1
<6>[ 1552.856221][T1029@C2] [Audio:PIPE] user_msg_in.parameter3=0x0
<6>[ 1552.856224][T1029@C2] [Audio:SIPC] aud_send_cmd_no_wait no wait
<6>[ 1552.856227][T1029@C2] [Audio:SIPC] aud_send_msg,cmd: 0x34, value0: 0x5e, value1: 0x0,
<6>[ 1552.856232][T1029@C2] [Audio:SIPC]  value2: 0x1, value3: 0x0
<6>[ 1552.856236][T1029@C2] [Audio:SMSG] aud_smsg_send: dst=1, channel=2
<6>[ 1552.856729][T1029@C2] [ASoC:SC2730] dac switch 8,shift=3,set=1
<6>[ 1552.856854][T1029@C2] [ASoC:SC2730] dac switch 12,shift=2,set=1
<6>[ 1552.856961][T1029@C2] [ASoC:BOARD] Ext Spk Switch On
<6>[ 1552.857056][T1029@C2] [ASoC:BOARD] Ext Spk1 Switch On
<6>[ 1552.857226][T1029@C2] [ASoC: VBC ] vbc_put_iis_tx_width_sel: 2530:vbc_put_iis_tx_width_sel, VBC_MUX_IIS_TX_DAC0=WD_24BIT
<6>[ 1552.857237][T1029@C2] [Audio:SIPC] audio_cmd_copy, write cmd para: txbuf_addr_v=0xffffffc00c18d000, tx_len=8
<6>[ 1552.857247][T1029@C2] [Audio:SMSG] warning aud_smsg_recv cache is empty!
<4>[ 1552.857251][T1029@C2] [Audio:SIPC] aud_recv_cmd channel =0 cmd=9 ENODATA
<6>[ 1552.857255][T1029@C2] [Audio:SIPC] aud_send_cmd in,cmd =9 id:29 ret-value:0
<6>[ 1552.857260][T1029@C2] [Audio:SIPC] aud_send_msg,cmd: 0x9, value0: 0x1d, value1: 0xffffffff,
<6>[ 1552.857265][T1029@C2] [Audio:SIPC]  value2: 0x94380000, value3: 0x0
<6>[ 1552.857270][T1029@C2] [Audio:SMSG] aud_smsg_send: dst=1, channel=0
<6>[ 1552.857321][T1029@C2] [Audio:SIPC] aud_send_cmd 688 wait dsp
<14>[ 1552.880263][ T1@C1] init: Sending signal 15 to service 'vendor.sprd.hardware.memtrack-service' (pid 569) process group...
<12>[ 1552.881039][ T1@C1] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_9999/pid_569 after 0 ms
<11>[ 1552.881123][ T1@C1] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_9999/pid_569: Device or resource busy
<14>[ 1552.894835][ T1@C3] init: Sending signal 15 to service 'vendor.enhance-default' (pid 566) process group...
<12>[ 1552.895508][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_566 after 0 ms
<11>[ 1552.895596][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_566: Device or resource busy
<14>[ 1552.896255][ T1@C3] init: Sending signal 15 to service 'vendor.cplog_svc-default' (pid 565) process group...
<12>[ 1552.897113][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_565 after 0 ms
<11>[ 1552.897200][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_565: Device or resource busy
<14>[ 1552.897928][ T1@C3] init: Sending signal 15 to service 'vendor.wifi_hal_legacy' (pid 564) process group...
<12>[ 1552.898323][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1010/pid_564 after 0 ms
<11>[ 1552.898391][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1010/pid_564: Device or resource busy
<14>[ 1552.898848][ T1@C3] init: Sending signal 15 to service 'vendor.usb_default' (pid 563) process group...
<12>[ 1552.899193][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_563 after 0 ms
<11>[ 1552.899250][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_563: Device or resource busy
<14>[ 1552.900014][ T1@C3] init: Sending signal 15 to service 'vendor.sensors-hal-multihal' (pid 561) process group...
<12>[ 1552.903410][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_561 after 0 ms
<11>[ 1552.903514][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_561: Device or resource busy
<14>[ 1552.904257][ T1@C3] init: Sending signal 15 to service 'media.unisoc.codec2' (pid 560) process group...
<12>[ 1552.905068][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1046/pid_560 after 0 ms
<11>[ 1552.905138][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1046/pid_560: Device or resource busy
<14>[ 1552.905686][ T1@C3] init: Sending signal 15 to service 'vendor.health-default' (pid 559) process group...
<12>[ 1552.906009][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_559 after 0 ms
<11>[ 1552.906063][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_559: Device or resource busy
<14>[ 1552.906415][ T1@C3] init: Sending signal 15 to service 'vendor.hwcomposer-2-4' (pid 558) process group...
<12>[ 1552.908366][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_558 after 0 ms
<11>[ 1552.908448][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_558: Device or resource busy
<14>[ 1552.909766][ T1@C3] init: Sending signal 15 to service 'vendor.gralloc-4-0' (pid 557) process group...
<12>[ 1552.910962][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_557 after 0 ms
<11>[ 1552.911053][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_557: Device or resource busy
<14>[ 1552.911747][ T1@C3] init: Sending signal 15 to service 'vendor.gatekeeper-1-0' (pid 556) process group...
<12>[ 1552.912404][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_556 after 0 ms
<11>[ 1552.912474][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_556: Device or resource busy
<14>[ 1552.912982][ T1@C3] init: Sending signal 15 to service 'vendor.drm-widevine-hal' (pid 552) process group...
<12>[ 1552.913364][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1013/pid_552 after 0 ms
<11>[ 1552.913427][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1013/pid_552: Device or resource busy
<14>[ 1552.913839][ T1@C3] init: Sending signal 15 to service 'vendor.drm-clearkey-service' (pid 551) process group...
<12>[ 1552.914417][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1013/pid_551 after 0 ms
<11>[ 1552.914479][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1013/pid_551: Device or resource busy
<14>[ 1552.915478][ T1@C3] init: Sending signal 15 to service 'vendor.camera-provider-2-4' (pid 550) process group...
<12>[ 1552.917158][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1047/pid_550 after 0 ms
<11>[ 1552.917246][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1047/pid_550: Device or resource busy
<14>[ 1552.917984][ T1@C3] init: Sending signal 15 to service 'vendor.bluetooth-1-1' (pid 549) process group...
<12>[ 1552.918405][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1002/pid_549 after 0 ms
<11>[ 1552.918469][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1002/pid_549: Device or resource busy
<14>[ 1552.918909][ T1@C3] init: Sending signal 15 to service 'vendor.audio-hal' (pid 548) process group...
<12>[ 1552.919595][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1041/pid_548 after 0 ms
<11>[ 1552.919653][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1041/pid_548: Device or resource busy
<14>[ 1552.921474][ T1@C3] init: Sending signal 15 to service 'hidl_memory' (pid 547) process group...
<6>[ 1552.921699][T881@C4] [Audio:SMSG] aud_smsg_recv wait interrupted!
<3>[ 1552.921710][T881@C4] [Audio:PIPE] aud_pipe_recv_cmd, Failed to recv,ret(-512)
<3>[ 1552.921719][T881@C4] [Audio:PIPE] aud_pipe_read aud_pipe_recv_cmd failed
<6>[ 1552.921725][T881@C4] [Audio:PIPE] user_msg_out.channel=0x0
<6>[ 1552.921728][T881@C4] [Audio:PIPE] user_msg_out.command=0x0
<6>[ 1552.921732][T881@C4] [Audio:PIPE] user_msg_out.parameter0=0x0
<6>[ 1552.921735][T881@C4] [Audio:PIPE] user_msg_out.parameter1=0x0
<6>[ 1552.921737][T881@C4] [Audio:PIPE] user_msg_out.parameter2=0x0
<6>[ 1552.921740][T881@C4] [Audio:PIPE] user_msg_out.parameter3=0x0
<12>[ 1552.922107][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_547 after 0 ms
<11>[ 1552.922177][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_547: Device or resource busy
<6>[ 1552.929258][T1029@C4] [Audio:SIPC] aud_recv_cmd, chan: 0x0, cmd: 0x9, value0: 0x1d, value1: 0xffffffff,
<6>[ 1552.929326][T1029@C4] [Audio:SIPC]  value2: 0x94380000, value3: 0x0, timeout: 750
<6>[ 1552.929338][T1029@C4] [Audio:SIPC] aud_send_cmd out,cmd =9 id:29 ret-value:0,repeat_count=1
<3>[ 1552.934152][T891@C6] sprd-apipe apipe-cmd-in: wait interrupted!
<14>[ 1552.946120][ T1@C3] init: Sending signal 15 to service 'zygote_secondary' (pid 539) process group...
<12>[ 1552.947122][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_539 after 0 ms
<11>[ 1552.947214][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_539: Device or resource busy
<14>[ 1553.014341][ T1@C5] init: Sending signal 15 to service 'zygote' (pid 538) process group...
<14>[ 1553.175913][T12798@C3] watchdogd: watchdogd started (interval 10, margin 30)!
<11>[ 1553.176281][T12798@C3] watchdogd: Failed to open /dev/watchdog: No such file or directory
<12>[ 1553.192188][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_538 after 0 ms
<11>[ 1553.192328][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_538: Device or resource busy
<6>[ 1553.253008][T716@C2] unisoc_userlog: userlog_release
<14>[ 1553.331980][ T1@C4] init: Sending signal 15 to service 'netd' (pid 537) process group...
<12>[ 1553.333023][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_537 after 0 ms
<11>[ 1553.333119][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_537: Device or resource busy
<14>[ 1553.375535][ T1@C3] init: Sending signal 15 to service 'statsd' (pid 536) process group...
<12>[ 1553.376332][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1066/pid_536 after 0 ms
<11>[ 1553.376416][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1066/pid_536: Device or resource busy
<14>[ 1553.377386][ T1@C3] init: Sending signal 15 to service 'ylog' (pid 525) process group...
<12>[ 1553.377858][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_525 after 0 ms
<11>[ 1553.377932][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_525: Device or resource busy
<14>[ 1553.378449][ T1@C3] init: Sending signal 15 to service 'vendor.sprd.boot-hal-1-2' (pid 390) process group...
<12>[ 1553.378812][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_390 after 0 ms
<11>[ 1553.378873][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_390: Device or resource busy
<14>[ 1553.379258][ T1@C3] init: Sending signal 15 to service 'vendor.keymint-unisoc-2.0' (pid 389) process group...
<12>[ 1553.379578][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_389 after 0 ms
<11>[ 1553.379629][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_389: Device or resource busy
<14>[ 1553.379985][ T1@C3] init: Sending signal 15 to service 'keystore2' (pid 386) process group...
<6>[ 1553.484968][T969@C6] [Audio:SMSG] aud_smsg_recv wait interrupted!
<3>[ 1553.484996][T969@C6] [Audio:PIPE] aud_pipe_recv_cmd, Failed to recv,ret(-512)
<3>[ 1553.485003][T969@C6] [Audio:PIPE] aud_pipe_read aud_pipe_recv_cmd failed
<6>[ 1553.485009][T969@C6] [Audio:PIPE] user_msg_out.channel=0x0
<6>[ 1553.485012][T969@C6] [Audio:PIPE] user_msg_out.command=0x0
<6>[ 1553.485014][T969@C6] [Audio:PIPE] user_msg_out.parameter0=0x0
<6>[ 1553.485016][T969@C6] [Audio:PIPE] user_msg_out.parameter1=0x0
<6>[ 1553.485018][T969@C6] [Audio:PIPE] user_msg_out.parameter2=0x0
<6>[ 1553.485021][T969@C6] [Audio:PIPE] user_msg_out.parameter3=0x0
<12>[ 1553.492163][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1017/pid_386 after 0 ms
<11>[ 1553.492289][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1017/pid_386: Device or resource busy
<14>[ 1553.527623][ T1@C2] init: Sending signal 15 to service 'system_suspend' (pid 381) process group...
<12>[ 1553.528496][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_381 after 0 ms
<11>[ 1553.528584][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_381: Device or resource busy
<14>[ 1553.529251][ T1@C2] init: Sending signal 15 to service 'lmkd' (pid 305) process group...
<12>[ 1553.529773][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1069/pid_305 after 0 ms
<11>[ 1553.529838][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1069/pid_305: Device or resource busy
<14>[ 1553.530267][ T1@C2] init: Sending signal 15 to service 'prng_seeder' (pid 283) process group...
<12>[ 1553.530603][ T1@C2] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1092/pid_283 after 0 ms
<11>[ 1553.530661][ T1@C2] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1092/pid_283: Device or resource busy
<3>[ 1553.626170][T9467@C0] |__c2e     mmc0: 5026    0    0    2   13   19    0    0    0    0    0    0    0    0
<3>[ 1553.626229][T9467@C0] |__d2e     mmc0: 4928   17    8    4   13   24    0    0    0    0    0    0    0    0
<3>[ 1553.626241][T9467@C0] |__blocks  mmc0:    0    0    0    0 3104 1026  470  221  135   23    7    8    0    0
<3>[ 1553.626251][T9467@C0] |__speed   mmc0: r= 33.32M/s, w= 93.88M/s, r_blk= 113760, w_blk= 9024
<6>[ 1553.980964][T606@C5] gs_close: ttyGS1 (0000000065970bed,000000002b754d42) ...
<6>[ 1553.981113][T606@C5] gs_close: ttyGS0 (0000000032a07176,0000000031b6056b) ...
<14>[ 1554.068091][T318@C2] servicemanager: Caller(pid=12799,uid=1000,sid=u:r:blank_screen:s0) Found android.hardware.light.ILights/default in device VINTF manifest.
<6>[ 1554.073397][    C4] mmc0 qcnt:3 cmdq_cnt:0 swcq->cmdq_mode: True
<6>[ 1554.073439][    C4] mmc0 random_cnt:0 sequential_cnt: 0 reason:4
<14>[ 1554.080719][ T1@C0] init: Service 'prng_seeder' (pid 283) received signal 15
<14>[ 1554.080783][ T1@C0] init: Sending signal 9 to service 'prng_seeder' (pid 283) process group...
<14>[ 1554.081486][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1092/pid_283
<14>[ 1554.103359][ T1@C4] init: Service 'system_suspend' (pid 381) received signal 15
<14>[ 1554.103421][ T1@C4] init: Sending signal 9 to service 'system_suspend' (pid 381) process group...
<14>[ 1554.104037][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_381
<14>[ 1554.105267][ T1@C4] init: Service 'vendor.keymint-unisoc-2.0' (pid 389) received signal 15
<14>[ 1554.108698][ T1@C4] init: Sending signal 9 to service 'vendor.keymint-unisoc-2.0' (pid 389) process group...
<14>[ 1554.109552][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_389
<14>[ 1554.111719][ T1@C4] init: Service 'hidl_memory' (pid 547) received signal 15
<14>[ 1554.111765][ T1@C4] init: Sending signal 9 to service 'hidl_memory' (pid 547) process group...
<14>[ 1554.112197][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_547
<14>[ 1554.113041][ T1@C4] init: Service 'vendor.bluetooth-1-1' (pid 549) received signal 15
<14>[ 1554.113070][ T1@C4] init: Sending signal 9 to service 'vendor.bluetooth-1-1' (pid 549) process group...
<14>[ 1554.113489][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1002/pid_549
<14>[ 1554.114206][ T1@C4] init: Service 'vendor.drm-clearkey-service' (pid 551) received signal 15
<14>[ 1554.114230][ T1@C4] init: Sending signal 9 to service 'vendor.drm-clearkey-service' (pid 551) process group...
<14>[ 1554.114566][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_551
<14>[ 1554.115238][ T1@C4] init: Service 'vendor.drm-widevine-hal' (pid 552) received signal 15
<14>[ 1554.115270][ T1@C4] init: Sending signal 9 to service 'vendor.drm-widevine-hal' (pid 552) process group...
<14>[ 1554.115610][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_552
<14>[ 1554.116252][ T1@C4] init: Service 'vendor.gatekeeper-1-0' (pid 556) received signal 15
<14>[ 1554.116276][ T1@C4] init: Sending signal 9 to service 'vendor.gatekeeper-1-0' (pid 556) process group...
<14>[ 1554.116575][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_556
<14>[ 1554.117176][ T1@C4] init: Service 'vendor.gralloc-4-0' (pid 557) received signal 15
<14>[ 1554.117198][ T1@C4] init: Sending signal 9 to service 'vendor.gralloc-4-0' (pid 557) process group...
<14>[ 1554.117593][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_557
<14>[ 1554.154262][ T1@C4] init: Service 'vendor.sprd.boot-hal-1-2' (pid 390) received signal 15
<14>[ 1554.154312][ T1@C4] init: Sending signal 9 to service 'vendor.sprd.boot-hal-1-2' (pid 390) process group...
<14>[ 1554.154911][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_390
<6>[ 1554.166596][T647@C1] unisoc_userlog: userlog_release
<14>[ 1554.225070][ T1@C4] init: Service 'vendor.health-default' (pid 559) received signal 15
<14>[ 1554.225134][ T1@C4] init: Sending signal 9 to service 'vendor.health-default' (pid 559) process group...
<14>[ 1554.225862][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_559
<14>[ 1554.238203][ T1@C4] init: Service 'media.unisoc.codec2' (pid 560) received signal 15
<14>[ 1554.238254][ T1@C4] init: Sending signal 9 to service 'media.unisoc.codec2' (pid 560) process group...
<14>[ 1554.238810][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_560
<14>[ 1554.275613][ T1@C3] init: Service 'netd' (pid 537) received signal 15
<14>[ 1554.275682][ T1@C3] init: Sending signal 9 to service 'netd' (pid 537) process group...
<14>[ 1554.276374][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_537
<14>[ 1554.316646][ T1@C3] init: Service 'vendor.usb_default' (pid 563) received signal 15
<14>[ 1554.316696][ T1@C3] init: Sending signal 9 to service 'vendor.usb_default' (pid 563) process group...
<14>[ 1554.317157][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_563
<14>[ 1554.317943][ T1@C3] init: Service 'vendor.wifi_hal_legacy' (pid 564) received signal 15
<14>[ 1554.317970][ T1@C3] init: Sending signal 9 to service 'vendor.wifi_hal_legacy' (pid 564) process group...
<14>[ 1554.318336][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1010/pid_564
<14>[ 1554.318966][ T1@C3] init: Service 'vendor.enhance-default' (pid 566) received signal 15
<14>[ 1554.318989][ T1@C3] init: Sending signal 9 to service 'vendor.enhance-default' (pid 566) process group...
<14>[ 1554.319301][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_566
<14>[ 1554.319919][ T1@C3] init: Service 'vendor.sprd.hardware.memtrack-service' (pid 569) received signal 15
<14>[ 1554.319940][ T1@C3] init: Sending signal 9 to service 'vendor.sprd.hardware.memtrack-service' (pid 569) process group...
<14>[ 1554.320227][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_569
<14>[ 1554.320823][ T1@C3] init: Service 'vendor.network-default' (pid 570) received signal 15
<14>[ 1554.320846][ T1@C3] init: Sending signal 9 to service 'vendor.network-default' (pid 570) process group...
<14>[ 1554.321158][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_570
<14>[ 1554.322027][ T1@C3] init: Service 'vendor.power.stats-default' (pid 572) received signal 15
<14>[ 1554.322062][ T1@C3] init: Sending signal 9 to service 'vendor.power.stats-default' (pid 572) process group...
<14>[ 1554.322477][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_572
<14>[ 1554.323397][ T1@C3] init: Service 'vendor.rebootescrow-default' (pid 573) received signal 15
<14>[ 1554.323430][ T1@C3] init: Sending signal 9 to service 'vendor.rebootescrow-default' (pid 573) process group...
<14>[ 1554.323805][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_573
<14>[ 1554.324472][ T1@C3] init: Service 'vendor.sprd.hardware.trusty-service' (pid 574) received signal 15
<14>[ 1554.324495][ T1@C3] init: Sending signal 9 to service 'vendor.sprd.hardware.trusty-service' (pid 574) process group...
<14>[ 1554.324815][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_574
<14>[ 1554.325506][ T1@C3] init: Service 'vendor.oemlock-default' (pid 575) received signal 15
<14>[ 1554.325529][ T1@C3] init: Sending signal 9 to service 'vendor.oemlock-default' (pid 575) process group...
<14>[ 1554.325828][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_575
<14>[ 1554.326486][ T1@C3] init: Service 'vendor.power-default' (pid 576) received signal 15
<14>[ 1554.326511][ T1@C3] init: Sending signal 9 to service 'vendor.power-default' (pid 576) process group...
<14>[ 1554.326838][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_576
<14>[ 1554.327485][ T1@C3] init: Service 'vendor.focaltech.fps_hal' (pid 577) received signal 15
<14>[ 1554.327508][ T1@C3] init: Sending signal 9 to service 'vendor.focaltech.fps_hal' (pid 577) process group...
<14>[ 1554.327796][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_577
<14>[ 1554.328404][ T1@C3] init: Service 'vendor.sprd.broadcastradio-hal2' (pid 579) received signal 15
<14>[ 1554.328428][ T1@C3] init: Sending signal 9 to service 'vendor.sprd.broadcastradio-hal2' (pid 579) process group...
<14>[ 1554.328720][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_579
<14>[ 1554.329475][ T1@C3] init: Service 'vendor.sprd.hardware.connmgr@1.0-service' (pid 580) received signal 15
<14>[ 1554.418674][ T1@C2] init: Sending signal 9 to service 'vendor.sprd.hardware.connmgr@1.0-service' (pid 580) process group...
<14>[ 1554.419389][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_580
<14>[ 1554.464046][ T1@C4] init: Service 'keystore2' (pid 386) received signal 15
<14>[ 1554.464113][ T1@C4] init: Sending signal 9 to service 'keystore2' (pid 386) process group...
<14>[ 1554.465372][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1017/pid_386
<14>[ 1554.469817][ T1@C0] init: Service 'miscdata_hal_service' (pid 581) received signal 15
<14>[ 1554.469870][ T1@C0] init: Sending signal 9 to service 'miscdata_hal_service' (pid 581) process group...
<14>[ 1554.470352][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_581
<14>[ 1554.471253][ T1@C0] init: Service 'vendor.thermal-hal-2-0' (pid 582) received signal 15
<14>[ 1554.471281][ T1@C0] init: Sending signal 9 to service 'vendor.thermal-hal-2-0' (pid 582) process group...
<14>[ 1554.471662][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_582
<14>[ 1554.472428][ T1@C0] init: Service 'vendor.tool-default' (pid 583) received signal 15
<14>[ 1554.472453][ T1@C0] init: Sending signal 9 to service 'vendor.tool-default' (pid 583) process group...
<14>[ 1554.472794][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_583
<14>[ 1554.473524][ T1@C0] init: Service 'vendor.sprd.hardware.vibrator-service' (pid 589) received signal 15
<14>[ 1554.473548][ T1@C0] init: Sending signal 9 to service 'vendor.sprd.hardware.vibrator-service' (pid 589) process group...
<14>[ 1554.473859][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_589
<14>[ 1554.474510][ T1@C0] init: Service 'credstore' (pid 591) received signal 15
<14>[ 1554.474534][ T1@C0] init: Sending signal 9 to service 'credstore' (pid 591) process group...
<14>[ 1554.474829][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1076/pid_591
<14>[ 1554.475518][ T1@C0] init: Service 'audio_parameter_parser_service' (pid 603) received signal 15
<14>[ 1554.475541][ T1@C0] init: Sending signal 9 to service 'audio_parameter_parser_service' (pid 603) process group...
<14>[ 1554.475850][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_603
<14>[ 1554.476520][ T1@C0] init: Service 'audio_tunning_service' (pid 604) received signal 15
<14>[ 1554.476542][ T1@C0] init: Sending signal 9 to service 'audio_tunning_service' (pid 604) process group...
<14>[ 1554.476827][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_604
<14>[ 1554.477706][ T1@C0] init: Service 'vendor.engpcclientlte' (pid 606) received signal 15 oneshot service took 5101.344238 seconds in background
<14>[ 1554.477733][ T1@C0] init: Sending signal 9 to service 'vendor.engpcclientlte' (pid 606) process group...
<14>[ 1554.478057][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_606
<14>[ 1554.478769][ T1@C0] init: Service 'gpsd' (pid 607) exited with status 0
<14>[ 1554.478792][ T1@C0] init: Sending signal 9 to service 'gpsd' (pid 607) process group...
<14>[ 1554.479087][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_607
<14>[ 1554.479731][ T1@C0] init: Service 'vendor.modem_control' (pid 608) received signal 15
<14>[ 1554.479756][ T1@C0] init: Sending signal 9 to service 'vendor.modem_control' (pid 608) process group...
<14>[ 1554.480035][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_608
<14>[ 1554.480777][ T1@C0] init: Service 'vendor.rpmbproxy' (pid 609) received signal 15 oneshot service took 5101.326172 seconds in background
<14>[ 1554.480809][ T1@C0] init: Sending signal 9 to service 'vendor.rpmbproxy' (pid 609) process group...
<14>[ 1554.481431][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_609
<14>[ 1554.482505][ T1@C0] init: Service 'vendor.nsproxy' (pid 611) received signal 15
<14>[ 1554.482539][ T1@C0] init: Sending signal 9 to service 'vendor.nsproxy' (pid 611) process group...
<14>[ 1554.482900][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_611
<14>[ 1554.483601][ T1@C0] init: Service 'vendor.tee_rpc' (pid 612) received signal 15
<14>[ 1554.483624][ T1@C0] init: Sending signal 9 to service 'vendor.tee_rpc' (pid 612) process group...
<14>[ 1554.483933][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_612
<14>[ 1554.484569][ T1@C0] init: Service 'vendor.tsupplicant' (pid 613) received signal 15
<14>[ 1554.484592][ T1@C0] init: Sending signal 9 to service 'vendor.tsupplicant' (pid 613) process group...
<14>[ 1554.484888][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_613
<14>[ 1554.485560][ T1@C0] init: Service 'vendor.ril-daemon' (pid 614) received signal 15
<14>[ 1554.485583][ T1@C0] init: Sending signal 9 to service 'vendor.ril-daemon' (pid 614) process group...
<14>[ 1554.485865][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1001/pid_614
<14>[ 1554.486634][ T1@C0] init: Service 'drm' (pid 704) received signal 15
<14>[ 1554.486660][ T1@C0] init: Sending signal 9 to service 'drm' (pid 704) process group...
<14>[ 1554.487064][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1019/pid_704
<14>[ 1554.487728][ T1@C0] init: Service 'traced_probes' (pid 705) received signal 15
<14>[ 1554.487754][ T1@C0] init: Sending signal 9 to service 'traced_probes' (pid 705) process group...
<14>[ 1554.488094][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_705
<14>[ 1554.488787][ T1@C0] init: Service 'traced' (pid 706) received signal 15
<14>[ 1554.488815][ T1@C0] init: Sending signal 9 to service 'traced' (pid 706) process group...
<14>[ 1554.489168][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_706
<14>[ 1554.491737][ T1@C0] init: Service 'vendor.identity-default' (pid 713) received signal 15
<14>[ 1554.491786][ T1@C0] init: Sending signal 9 to service 'vendor.identity-default' (pid 713) process group...
<14>[ 1554.492301][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_713
<14>[ 1554.493082][ T1@C0] init: Service 'nfc_hal_service.tms.aidl' (pid 714) received signal 15
<14>[ 1554.493107][ T1@C0] init: Sending signal 9 to service 'nfc_hal_service.tms.aidl' (pid 714) process group...
<14>[ 1554.493494][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1027/pid_714
<14>[ 1554.494212][ T1@C0] init: Service 'vendor.teensproxy' (pid 715) received signal 15
<14>[ 1554.494239][ T1@C0] init: Sending signal 9 to service 'vendor.teensproxy' (pid 715) process group...
<14>[ 1554.494916][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_715
<14>[ 1554.495931][ T1@C0] init: Service 'uniview' (pid 716) received signal 15
<14>[ 1554.495963][ T1@C0] init: Sending signal 9 to service 'uniview' (pid 716) process group...
<14>[ 1554.496374][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_716
<14>[ 1554.497142][ T1@C0] init: Service 'incidentd' (pid 720) received signal 15
<14>[ 1554.497169][ T1@C0] init: Sending signal 9 to service 'incidentd' (pid 720) process group...
<14>[ 1554.497596][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1067/pid_720
<14>[ 1554.498352][ T1@C0] init: Service 'installd' (pid 723) received signal 15
<14>[ 1554.498379][ T1@C0] init: Sending signal 9 to service 'installd' (pid 723) process group...
<14>[ 1554.498723][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_723
<14>[ 1554.499441][ T1@C0] init: Service 'mediaextractor' (pid 724) received signal 15
<14>[ 1554.499467][ T1@C0] init: Sending signal 9 to service 'mediaextractor' (pid 724) process group...
<14>[ 1554.499807][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1040/pid_724
<14>[ 1554.501663][ T1@C0] init: Service 'mediametrics' (pid 725) received signal 15
<14>[ 1554.501695][ T1@C0] init: Sending signal 9 to service 'mediametrics' (pid 725) process group...
<14>[ 1554.502100][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_725
<14>[ 1554.502884][ T1@C0] init: Service 'media' (pid 726) received signal 15
<14>[ 1554.502908][ T1@C0] init: Sending signal 9 to service 'media' (pid 726) process group...
<14>[ 1554.503215][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_726
<14>[ 1554.503856][ T1@C0] init: Service 'storaged' (pid 728) received signal 15
<14>[ 1554.503880][ T1@C0] init: Sending signal 9 to service 'storaged' (pid 728) process group...
<14>[ 1554.504228][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_728
<14>[ 1554.505145][ T1@C0] init: Service 'ims_bridged' (pid 731) received signal 15
<14>[ 1554.505175][ T1@C0] init: Sending signal 9 to service 'ims_bridged' (pid 731) process group...
<14>[ 1554.505657][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_731
<14>[ 1554.507290][ T1@C0] init: Service 'wificond' (pid 730) exited with status 0
<14>[ 1554.507331][ T1@C0] init: Sending signal 9 to service 'wificond' (pid 730) process group...
<14>[ 1554.507766][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1010/pid_730
<14>[ 1554.508786][ T1@C0] init: Service 'linkturbonative' (pid 732) received signal 15
<14>[ 1554.508820][ T1@C0] init: Sending signal 9 to service 'linkturbonative' (pid 732) process group...
<14>[ 1554.509230][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_732
<14>[ 1554.514232][ T1@C0] init: Service 'remotedisplay' (pid 733) received signal 15
<14>[ 1554.514276][ T1@C0] init: Sending signal 9 to service 'remotedisplay' (pid 733) process group...
<14>[ 1554.514691][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_733
<14>[ 1554.516365][ T1@C0] init: Service 'slogmodem' (pid 734) received signal 15
<14>[ 1554.516418][ T1@C0] init: Sending signal 9 to service 'slogmodem' (pid 734) process group...
<14>[ 1554.516924][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_734
<14>[ 1554.518308][ T1@C0] init: Service 'uniresctlopt' (pid 735) received signal 15 oneshot service took 5100.464844 seconds in background
<14>[ 1554.518350][ T1@C0] init: Sending signal 9 to service 'uniresctlopt' (pid 735) process group...
<14>[ 1554.518779][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_735
<14>[ 1554.519856][ T1@C0] init: Service 'vendor.media.omx' (pid 736) received signal 15
<14>[ 1554.519894][ T1@C0] init: Sending signal 9 to service 'vendor.media.omx' (pid 736) process group...
<14>[ 1554.520300][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_736
<14>[ 1554.521351][ T1@C0] init: Service 'vendor.charged' (pid 743) received signal 15
<14>[ 1554.521389][ T1@C0] init: Sending signal 9 to service 'vendor.charged' (pid 743) process group...
<14>[ 1554.521757][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_743
<14>[ 1554.522503][ T1@C0] init: Service 'phasecheckserver' (pid 754) received signal 15
<14>[ 1554.522530][ T1@C0] init: Sending signal 9 to service 'phasecheckserver' (pid 754) process group...
<14>[ 1554.522868][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_754
<14>[ 1554.523543][ T1@C0] init: Service 'vendor.refnotify' (pid 758) received signal 15
<14>[ 1554.523567][ T1@C0] init: Sending signal 9 to service 'vendor.refnotify' (pid 758) process group...
<14>[ 1554.523929][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_758
<14>[ 1554.524610][ T1@C0] init: Service 'vendor.srtd' (pid 761) received signal 15
<14>[ 1554.524634][ T1@C0] init: Sending signal 9 to service 'vendor.srtd' (pid 761) process group...
<14>[ 1554.524956][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_761
<14>[ 1554.525669][ T1@C0] init: Service 'vendor.thermald' (pid 767) received signal 15
<14>[ 1554.525694][ T1@C0] init: Sending signal 9 to service 'vendor.thermald' (pid 767) process group...
<14>[ 1554.526039][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_767
<14>[ 1554.526716][ T1@C0] init: Service 'unionpnp_service' (pid 768) received signal 15
<14>[ 1554.526740][ T1@C0] init: Sending signal 9 to service 'unionpnp_service' (pid 768) process group...
<14>[ 1554.527068][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_768
<14>[ 1554.527916][ T1@C0] init: Service 'tool_service' (pid 784) received signal 15
<14>[ 1554.527947][ T1@C0] init: Sending signal 9 to service 'tool_service' (pid 784) process group...
<14>[ 1554.528344][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_784
<14>[ 1554.529059][ T1@C0] init: Service 'gatekeeperd' (pid 790) received signal 15
<14>[ 1554.529082][ T1@C0] init: Sending signal 9 to service 'gatekeeperd' (pid 790) process group...
<14>[ 1554.529440][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_790
<14>[ 1554.530133][ T1@C0] init: Service 'vendor.wcnd' (pid 793) received signal 15
<14>[ 1554.530157][ T1@C0] init: Sending signal 9 to service 'vendor.wcnd' (pid 793) process group...
<14>[ 1554.530457][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_793
<14>[ 1554.531140][ T1@C0] init: Service 'vendor.prodproxy' (pid 795) received signal 15 oneshot service took 5100.256836 seconds in background
<14>[ 1554.531163][ T1@C0] init: Sending signal 9 to service 'vendor.prodproxy' (pid 795) process group...
<14>[ 1554.531452][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_795
<14>[ 1554.532137][ T1@C0] init: Service 'vendor.wcn_chr' (pid 799) received signal 15
<14>[ 1554.532163][ T1@C0] init: Sending signal 9 to service 'vendor.wcn_chr' (pid 799) process group...
<14>[ 1554.536091][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_12345/pid_799
<14>[ 1554.537370][ T1@C0] init: Service 'vendor.cp_diskserver' (pid 985) received signal 15
<14>[ 1554.537413][ T1@C0] init: Sending signal 9 to service 'vendor.cp_diskserver' (pid 985) process group...
<14>[ 1554.537820][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_985
<14>[ 1554.538544][ T1@C0] init: Service 'srmi_proxyd' (pid 1218) received signal 15
<14>[ 1554.538567][ T1@C0] init: Sending signal 9 to service 'srmi_proxyd' (pid 1218) process group...
<4>[ 1554.538707][T958@C3] [FTS_TS]fts_ps_release: enter
<14>[ 1554.538927][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_1218
<14>[ 1554.540032][ T1@C0] init: Service 'vendor.sensors-hal-multihal' (pid 561) received signal 15
<14>[ 1554.540076][ T1@C0] init: Sending signal 9 to service 'vendor.sensors-hal-multihal' (pid 561) process group...
<14>[ 1554.540613][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_561
<6>[ 1554.574898][T12665@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[ 1554.583608][T565@C1] [Audio:DSPDUMP] audio_dsp_release
<14>[ 1554.604004][ T1@C0] init: Service 'ylog' (pid 525) received signal 9
<14>[ 1554.604061][ T1@C0] init: Sending signal 9 to service 'ylog' (pid 525) process group...
<14>[ 1554.604668][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_525
<14>[ 1554.631478][ T1@C5] init: Service 'bootanim' (pid 12414) received signal 15 oneshot service took 5.975000 seconds in background
<14>[ 1554.631526][ T1@C5] init: Sending signal 9 to service 'bootanim' (pid 12414) process group...
<14>[ 1554.631969][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1003/pid_12414
<14>[ 1554.634510][ T1@C5] init: Service 'watchdogd' (pid 12798) exited with status 1 oneshot service took 2.527000 seconds in background
<14>[ 1554.634558][ T1@C5] init: Sending signal 9 to service 'watchdogd' (pid 12798) process group...
<14>[ 1554.635035][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_12798
<14>[ 1554.685978][ T1@C5] init: Service 'vendor.cplog_svc-default' (pid 565) received signal 15
<14>[ 1554.686042][ T1@C5] init: Sending signal 9 to service 'vendor.cplog_svc-default' (pid 565) process group...
<14>[ 1554.686670][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_565
<14>[ 1554.707355][ T1@C0] init: Service 'blank_screen' (pid 12799) exited with status 0 oneshot service took 2.592000 seconds in background
<14>[ 1554.707416][ T1@C0] init: Sending signal 9 to service 'blank_screen' (pid 12799) process group...
<14>[ 1554.708054][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_12799
<14>[ 1554.709125][ T1@C0] init: Untracked pid 553 received signal 15
<14>[ 1554.709147][ T1@C0] init: Untracked pid 553 did not have an associated service entry and will not be reaped
<14>[ 1554.709232][ T1@C0] init: Untracked pid 554 received signal 15
<14>[ 1554.709246][ T1@C0] init: Untracked pid 554 did not have an associated service entry and will not be reaped
<14>[ 1554.737017][ T1@C0] init: Service 'zygote' (pid 538) received signal 15
<14>[ 1554.737080][ T1@C0] init: Sending signal 9 to service 'zygote' (pid 538) process group...
<14>[ 1554.738303][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_538
<6>[ 1554.853360][    C5] mmc0 qcnt:0 cmdq_cnt:0 swcq->cmdq_mode: False
<6>[ 1554.853422][    C5] mmc0 random_cnt:0 sequential_cnt: 3 reason:1
<14>[ 1554.863865][ T1@C4] init: Service 'statsd' (pid 536) received signal 15
<14>[ 1554.863914][ T1@C4] init: Sending signal 9 to service 'statsd' (pid 536) process group...
<11>[ 1554.864069][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1066/pid_536/cgroup.kill: No such file or directory
<11>[ 1554.864116][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1066/pid_536/cgroup.procs: No such file or directory
<14>[ 1554.865058][ T1@C4] init: Service 'vendor.face-default' (pid 712) received signal 15
<14>[ 1554.865085][ T1@C4] init: Sending signal 9 to service 'vendor.face-default' (pid 712) process group...
<11>[ 1554.865186][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_712/cgroup.kill: No such file or directory
<11>[ 1554.865226][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_712/cgroup.procs: No such file or directory
<14>[ 1554.865957][ T1@C4] init: Untracked pid 6514 received signal 15
<14>[ 1554.865979][ T1@C4] init: Untracked pid 6514 did not have an associated service entry and will not be reaped
<14>[ 1554.866077][ T1@C4] init: Untracked pid 6515 received signal 15
<14>[ 1554.866092][ T1@C4] init: Untracked pid 6515 did not have an associated service entry and will not be reaped
<14>[ 1554.866187][ T1@C4] init: Untracked pid 7232 received signal 15
<14>[ 1554.866201][ T1@C4] init: Untracked pid 7232 did not have an associated service entry and will not be reaped
<14>[ 1554.866282][ T1@C4] init: Untracked pid 7790 received signal 15
<14>[ 1554.866297][ T1@C4] init: Untracked pid 7790 did not have an associated service entry and will not be reaped
<14>[ 1554.866383][ T1@C4] init: Untracked pid 12477 received signal 15
<14>[ 1554.866397][ T1@C4] init: Untracked pid 12477 did not have an associated service entry and will not be reaped
<14>[ 1554.866751][ T1@C4] init: Untracked pid 12562 received signal 15
<14>[ 1554.866767][ T1@C4] init: Untracked pid 12562 did not have an associated service entry and will not be reaped
<14>[ 1554.867000][ T1@C4] init: Untracked pid 12682 received signal 15
<14>[ 1554.867015][ T1@C4] init: Untracked pid 12682 did not have an associated service entry and will not be reaped
<14>[ 1554.867184][ T1@C4] init: Untracked pid 12705 received signal 15
<14>[ 1554.867198][ T1@C4] init: Untracked pid 12705 did not have an associated service entry and will not be reaped
<14>[ 1554.867365][ T1@C4] init: Untracked pid 12722 received signal 15
<14>[ 1554.867379][ T1@C4] init: Untracked pid 12722 did not have an associated service entry and will not be reaped
<14>[ 1554.867542][ T1@C4] init: Untracked pid 12741 received signal 15
<14>[ 1554.867557][ T1@C4] init: Untracked pid 12741 did not have an associated service entry and will not be reaped
<14>[ 1554.930194][ T1@C1] init: Untracked pid 2216 received signal 15
<14>[ 1554.930262][ T1@C1] init: Untracked pid 2216 did not have an associated service entry and will not be reaped
<6>[ 1554.964093][T10@C5] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[ 1554.964134][T10@C5] WCN BASE: tx:at+loopcheck=1554946,17141
<6>[ 1554.964134][T10@C5] 
<6>[ 1554.964323][T10@C5] WCN BASE: mdbg_tx_cb, chn:0
<6>[ 1554.981884][T383@C5] WCN BASE: : rx:loopcheck_ack:ap_send=1554946,cp2_bootup=17141,cp2_send=5108007
<6>[ 1554.981884][T383@C5] 
<6>[ 1554.982096][T10@C5] WCN BASE: loopcheck(301) 2025-08-01_03:23:20.583198531
<6>[ 1554.985732][T12065@C5] binder: undelivered death notification, b4000077d8c529a0
<6>[ 1554.985771][T12065@C5] binder: undelivered death notification, b4000077d8c56e60
<14>[ 1554.986100][ T1@C4] init: Untracked pid 2067 received signal 15
<14>[ 1554.986139][ T1@C4] init: Untracked pid 2067 did not have an associated service entry and will not be reaped
<14>[ 1555.000872][ T1@C4] init: Untracked pid 2172 received signal 15
<14>[ 1555.000939][ T1@C4] init: Untracked pid 2172 did not have an associated service entry and will not be reaped
<14>[ 1555.018698][ T1@C4] init: Service 'ext_data' (pid 769) received signal 15
<14>[ 1555.018771][ T1@C4] init: Sending signal 9 to service 'ext_data' (pid 769) process group...
<14>[ 1555.019622][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_769
<6>[ 1555.079967][T1576@C5] (NULL net_device): sprd_cfg80211_stop_p2p_device
<4>[ 1555.080037][T1576@C5] sprd-wlan: [1555054]cid 2 tx[CMD_CLOSE]
<6>[ 1555.085021][T519@C0] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[ 1555.085087][T519@C0] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[ 1555.085093][T519@C0] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[ 1555.085143][T519@C0] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<4>[ 1555.085153][T519@C0] sc2355, tx_work_queue, fw_awake = 0
<6>[ 1555.097915][T533@C5] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:5076025(us)
<6>[ 1555.097997][T533@C5] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[ 1555.098003][T533@C5] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[ 1555.098009][T533@C5] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[ 1555.098014][T533@C5] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[ 1555.098033][T533@C5] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=365, cmd_send=365
<6>[ 1555.098042][T533@C5] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[ 1555.098054][T533@C5] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[ 1555.104568][T530@C1] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<6>[ 1555.111762][T852@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[ 1555.119679][T1576@C5] (NULL net_device): sprd_uninit_fw type 10, mode 4
<6>[ 1555.119709][T1576@C5] sprd-wlan: sprd_iface_set_power Power off WCN (2 time)
<14>[ 1555.141041][ T1@C5] init: Service 'vendor.hwcomposer-2-4' (pid 558) received signal 15
<14>[ 1555.141106][ T1@C5] init: Sending signal 9 to service 'vendor.hwcomposer-2-4' (pid 558) process group...
<14>[ 1555.141919][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_558
<14>[ 1555.203038][ T1@C4] init: Service 'zygote_secondary' (pid 539) received signal 15
<14>[ 1555.203106][ T1@C4] init: Sending signal 9 to service 'zygote_secondary' (pid 539) process group...
<14>[ 1555.203931][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_539
<14>[ 1555.213647][ T1@C5] init: Untracked pid 1438 received signal 15
<14>[ 1555.213700][ T1@C5] init: Untracked pid 1438 did not have an associated service entry and will not be reaped
<14>[ 1555.213871][ T1@C5] init: Untracked pid 1439 received signal 15
<14>[ 1555.213893][ T1@C5] init: Untracked pid 1439 did not have an associated service entry and will not be reaped
<14>[ 1555.214002][ T1@C5] init: Untracked pid 1440 received signal 15
<14>[ 1555.214018][ T1@C5] init: Untracked pid 1440 did not have an associated service entry and will not be reaped
<14>[ 1555.214122][ T1@C5] init: Untracked pid 1442 received signal 15
<14>[ 1555.214137][ T1@C5] init: Untracked pid 1442 did not have an associated service entry and will not be reaped
<14>[ 1555.337436][ T1@C4] init: Untracked pid 1613 received signal 15
<14>[ 1555.337494][ T1@C4] init: Untracked pid 1613 did not have an associated service entry and will not be reaped
<14>[ 1555.411013][ T1@C7] init: Service 'gpu' (pid 594) received signal 15
<14>[ 1555.411063][ T1@C7] init: Sending signal 9 to service 'gpu' (pid 594) process group...
<14>[ 1555.411598][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1072/pid_594
<14>[ 1555.466773][ T1@C5] init: Service 'cameraserver' (pid 718) received signal 15
<14>[ 1555.466825][ T1@C5] init: Sending signal 9 to service 'cameraserver' (pid 718) process group...
<14>[ 1555.467375][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1047/pid_718
<14>[ 1555.505277][ T1@C4] init: Untracked pid 12432 received signal 15
<14>[ 1555.505379][ T1@C4] init: Untracked pid 12432 did not have an associated service entry and will not be reaped
<6>[ 1555.530414][T11985@C4] binder: undelivered death notification, b4000077d8c5cdf0
<6>[ 1555.530449][T11985@C4] binder: undelivered death notification, b4000077d8c6ac00
<14>[ 1555.531196][ T1@C5] init: Untracked pid 2274 received signal 15
<14>[ 1555.531244][ T1@C5] init: Untracked pid 2274 did not have an associated service entry and will not be reaped
<6>[ 1555.532026][T7868@C4] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1555.538495][ T1@C7] init: Untracked pid 2296 received signal 15
<14>[ 1555.538532][ T1@C7] init: Untracked pid 2296 did not have an associated service entry and will not be reaped
<6>[ 1555.554100][T12065@C5] wlan0: frame_type 208, reg 0
<4>[ 1555.554169][T365@C5] sprd-wlan: [1555528]cid 0 tx[CMD_REGISTER_FRAME]
<6>[ 1555.554398][T519@C5] WCN BASE: [wcn_sipc_buf_list_alloc] 298 chn[16]
<6>[ 1555.554418][T519@C5] WCN BASE: [wcn_sipc_push_list_enqueue] 398 chn[16]
<6>[ 1555.554423][T519@C5] WCN BASE: [wcn_sipc_wakeup_tx] 326 chn[16]
<6>[ 1555.554457][T533@C5] WCN BASE: [wcn_sipc_work_func] 899 chn[16] during compelte pt:456519(us)
<6>[ 1555.554489][T533@C5] WCN BASE: [wcn_sipc_sblk_send] 626 chn[16]
<6>[ 1555.554494][T533@C5] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 672 chn[16]
<6>[ 1555.554499][T533@C5] WCN BASE: [wcn_sipc_pop_list_enqueue] 346 chn[16]
<6>[ 1555.554503][T533@C5] WCN BASE: [wcn_sipc_pop_list_flush] 358 chn[16]
<6>[ 1555.554511][T533@C5] sprd-wlan: tx_cmd_pop num: 1,cmd_poped=366, cmd_send=366
<6>[ 1555.554516][T533@C5] WCN BASE: [wcn_sipc_buf_list_free] 312 chn[16]
<6>[ 1555.554523][T533@C5] WCN BASE: [wcn_sipc_sblk_push_list_dequeue] 687 chn[16]
<6>[ 1555.554541][T519@C5] WCN BASE: [wcn_sipc_sblk_push] 720 chn[16]
<4>[ 1555.554547][T519@C5] sc2355, tx_work_queue, fw_awake = 0
<14>[ 1555.557117][ T1@C4] init: Service 'vendor.camera-provider-2-4' (pid 550) received signal 15
<14>[ 1555.557153][ T1@C4] init: Sending signal 9 to service 'vendor.camera-provider-2-4' (pid 550) process group...
<14>[ 1555.557744][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1047/pid_550
<6>[ 1555.565718][T530@C5] WCN BASE: wcn_sipc_sblk_notifer  785 index:17  event:2
<14>[ 1555.567868][ T1@C5] init: Untracked pid 6468 received signal 15
<14>[ 1555.567925][ T1@C5] init: Untracked pid 6468 did not have an associated service entry and will not be reaped
<14>[ 1555.735486][ T1@C5] init: Service 'audioserver' (pid 590) received signal 15
<14>[ 1555.735539][ T1@C5] init: Sending signal 9 to service 'audioserver' (pid 590) process group...
<14>[ 1555.736095][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_590
<14>[ 1555.770249][ T1@C5] init: Service 'lmkd' (pid 305) received signal 15
<14>[ 1555.770314][ T1@C5] init: Sending signal 9 to service 'lmkd' (pid 305) process group...
<14>[ 1555.770936][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1069/pid_305
<14>[ 1555.827385][ T1@C4] init: Untracked pid 12291 received signal 15
<14>[ 1555.827445][ T1@C4] init: Untracked pid 12291 did not have an associated service entry and will not be reaped
<14>[ 1555.914954][ T1@C4] init: Untracked pid 1711 received signal 15
<14>[ 1555.914995][ T1@C4] init: Untracked pid 1711 did not have an associated service entry and will not be reaped
<14>[ 1555.931220][ T1@C4] init: Service 'media.swcodec' (pid 787) received signal 15
<14>[ 1555.931283][ T1@C4] init: Sending signal 9 to service 'media.swcodec' (pid 787) process group...
<14>[ 1555.931953][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_787
<14>[ 1556.011798][ T1@C5] init: Untracked pid 7240 received signal 15
<14>[ 1556.011858][ T1@C5] init: Untracked pid 7240 did not have an associated service entry and will not be reaped
<14>[ 1556.018575][ T1@C5] init: Service 'vendor.sprd.hardware.gnss-service' (pid 578) received signal 15
<14>[ 1556.018627][ T1@C5] init: Sending signal 9 to service 'vendor.sprd.hardware.gnss-service' (pid 578) process group...
<14>[ 1556.019185][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_578
<14>[ 1556.125904][ T1@C5] init: Untracked pid 6724 received signal 15
<14>[ 1556.125964][ T1@C5] init: Untracked pid 6724 did not have an associated service entry and will not be reaped
<14>[ 1556.207029][ T1@C5] init: Untracked pid 12334 received signal 15
<14>[ 1556.207088][ T1@C5] init: Untracked pid 12334 did not have an associated service entry and will not be reaped
<14>[ 1556.280770][ T1@C5] init: Service 'wpa_supplicant' (pid 1576) exited with status 0 oneshot service took 5091.425781 seconds in background
<14>[ 1556.280835][ T1@C5] init: Sending signal 9 to service 'wpa_supplicant' (pid 1576) process group...
<14>[ 1556.281520][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_1576
<6>[ 1556.337428][T2382@C4] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1556.365859][ T1@C2] init: Untracked pid 2348 received signal 15
<14>[ 1556.365897][ T1@C2] init: Untracked pid 2348 did not have an associated service entry and will not be reaped
<14>[ 1556.496197][ T1@C2] init: Untracked pid 12238 received signal 15
<14>[ 1556.496256][ T1@C2] init: Untracked pid 12238 did not have an associated service entry and will not be reaped
<14>[ 1556.560520][ T1@C2] init: Waiting for 97 pids to be reaped took 3009ms with 4 of them still running
<14>[ 1556.627025][ T1@C2] init: Still running: 791
<14>[ 1556.627074][ T1@C2] init: Name:	update_engine
<14>[ 1556.627081][ T1@C2] init: Umask:	0077
<14>[ 1556.627088][ T1@C2] init: State:	D (disk sleep)
<14>[ 1556.627094][ T1@C2] init: Tgid:	791
<14>[ 1556.627100][ T1@C2] init: Ngid:	0
<14>[ 1556.627106][ T1@C2] init: Pid:	791
<14>[ 1556.627112][ T1@C2] init: PPid:	1
<14>[ 1556.627118][ T1@C2] init: TracerPid:	0
<14>[ 1556.627124][ T1@C2] init: Uid:	0	0	0	0
<14>[ 1556.627129][ T1@C2] init: Gid:	0	0	0	0
<14>[ 1556.627135][ T1@C2] init: FDSize:	64
<14>[ 1556.627141][ T1@C2] init: Groups:	1000 1005 1023 ************** 9997 
<14>[ 1556.627148][ T1@C2] init: VmPeak:	10883140 kB
<14>[ 1556.627153][ T1@C2] init: VmSize:	10883140 kB
<14>[ 1556.627159][ T1@C2] init: VmLck:	       0 kB
<14>[ 1556.627165][ T1@C2] init: VmPin:	       0 kB
<14>[ 1556.627171][ T1@C2] init: VmHWM:	   11000 kB
<14>[ 1556.627177][ T1@C2] init: VmRSS:	    6536 kB
<14>[ 1556.627183][ T1@C2] init: RssAnon:	     116 kB
<14>[ 1556.627189][ T1@C2] init: RssFile:	    6128 kB
<14>[ 1556.627194][ T1@C2] init: RssShmem:	     292 kB
<14>[ 1556.627200][ T1@C2] init: VmData:	    7412 kB
<14>[ 1556.627205][ T1@C2] init: VmStk:	     136 kB
<14>[ 1556.627210][ T1@C2] init: VmExe:	    2236 kB
<14>[ 1556.627215][ T1@C2] init: VmLib:	    7592 kB
<14>[ 1556.627221][ T1@C2] init: VmPTE:	     224 kB
<14>[ 1556.627226][ T1@C2] init: VmSwap:	    1352 kB
<14>[ 1556.627231][ T1@C2] init: CoreDumping:	0
<14>[ 1556.627237][ T1@C2] init: THP_enabled:	1
<14>[ 1556.627242][ T1@C2] init: Threads:	1
<14>[ 1556.627247][ T1@C2] init: SigQ:	1/13409
<14>[ 1556.627252][ T1@C2] init: SigPnd:	0000000000000000
<14>[ 1556.627258][ T1@C2] init: ShdPnd:	0000000000000000
<14>[ 1556.627264][ T1@C2] init: SigBlk:	0000000080014003
<14>[ 1556.627270][ T1@C2] init: SigIgn:	0000002000000000
<14>[ 1556.627276][ T1@C2] init: SigCgt:	0000004c4000c4f8
<14>[ 1556.627281][ T1@C2] init: CapInh:	0000000000000000
<14>[ 1556.627287][ T1@C2] init: CapPrm:	000001ffffffffff
<14>[ 1556.627292][ T1@C2] init: CapEff:	000001ffffffffff
<14>[ 1556.627297][ T1@C2] init: CapBnd:	000001ffffffffff
<14>[ 1556.627303][ T1@C2] init: CapAmb:	0000000000000000
<14>[ 1556.627309][ T1@C2] init: NoNewPrivs:	0
<14>[ 1556.627315][ T1@C2] init: Seccomp:	0
<14>[ 1556.627320][ T1@C2] init: Seccomp_filters:	0
<14>[ 1556.627326][ T1@C2] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1556.627333][ T1@C2] init: SpeculationIndirectBranch:	unknown
<14>[ 1556.627338][ T1@C2] init: Cpus_allowed:	0f
<14>[ 1556.627343][ T1@C2] init: Cpus_allowed_list:	0-3
<14>[ 1556.627349][ T1@C2] init: Mems_allowed:	1
<14>[ 1556.627354][ T1@C2] init: Mems_allowed_list:	0
<14>[ 1556.627360][ T1@C2] init: voluntary_ctxt_switches:	238
<14>[ 1556.627365][ T1@C2] init: nonvoluntary_ctxt_switches:	302
<14>[ 1556.627371][ T1@C2] init: 
<14>[ 1556.627869][ T1@C2] init: Still running: 747
<14>[ 1556.627893][ T1@C2] init: Name:	uniber
<14>[ 1556.627899][ T1@C2] init: Umask:	0077
<14>[ 1556.627906][ T1@C2] init: State:	S (sleeping)
<14>[ 1556.627911][ T1@C2] init: Tgid:	747
<14>[ 1556.627918][ T1@C2] init: Ngid:	0
<14>[ 1556.627923][ T1@C2] init: Pid:	747
<14>[ 1556.627929][ T1@C2] init: PPid:	1
<14>[ 1556.627934][ T1@C2] init: TracerPid:	0
<14>[ 1556.627939][ T1@C2] init: Uid:	0	0	0	0
<14>[ 1556.627944][ T1@C2] init: Gid:	0	0	0	0
<14>[ 1556.627950][ T1@C2] init: FDSize:	64
<14>[ 1556.627956][ T1@C2] init: Groups:	 
<14>[ 1556.627961][ T1@C2] init: VmPeak:	     876 kB
<14>[ 1556.627967][ T1@C2] init: VmSize:	     876 kB
<14>[ 1556.627972][ T1@C2] init: VmLck:	       0 kB
<14>[ 1556.627979][ T1@C2] init: VmPin:	       0 kB
<14>[ 1556.627984][ T1@C2] init: VmHWM:	       8 kB
<14>[ 1556.627990][ T1@C2] init: VmRSS:	       0 kB
<14>[ 1556.627996][ T1@C2] init: RssAnon:	       0 kB
<14>[ 1556.628001][ T1@C2] init: RssFile:	       0 kB
<14>[ 1556.628007][ T1@C2] init: RssShmem:	       0 kB
<14>[ 1556.628013][ T1@C2] init: VmData:	     172 kB
<14>[ 1556.628019][ T1@C2] init: VmStk:	     136 kB
<14>[ 1556.628025][ T1@C2] init: VmExe:	     556 kB
<14>[ 1556.628031][ T1@C2] init: VmLib:	       4 kB
<14>[ 1556.628037][ T1@C2] init: VmPTE:	      28 kB
<14>[ 1556.628044][ T1@C2] init: VmSwap:	      12 kB
<14>[ 1556.628049][ T1@C2] init: CoreDumping:	0
<14>[ 1556.628055][ T1@C2] init: THP_enabled:	1
<14>[ 1556.628061][ T1@C2] init: Threads:	1
<14>[ 1556.628067][ T1@C2] init: SigQ:	2/13409
<14>[ 1556.628072][ T1@C2] init: SigPnd:	0000000000000000
<14>[ 1556.628078][ T1@C2] init: ShdPnd:	0000000000004000
<14>[ 1556.628083][ T1@C2] init: SigBlk:	fffffffe7ffbfefd
<14>[ 1556.628088][ T1@C2] init: SigIgn:	0000002000000000
<14>[ 1556.628094][ T1@C2] init: SigCgt:	0000000000000002
<14>[ 1556.628100][ T1@C2] init: CapInh:	0000000000000000
<14>[ 1556.628106][ T1@C2] init: CapPrm:	000001ffffffffff
<14>[ 1556.628112][ T1@C2] init: CapEff:	000001ffffffffff
<14>[ 1556.628117][ T1@C2] init: CapBnd:	000001ffffffffff
<14>[ 1556.628123][ T1@C2] init: CapAmb:	0000000000000000
<14>[ 1556.628128][ T1@C2] init: NoNewPrivs:	0
<14>[ 1556.628133][ T1@C2] init: Seccomp:	0
<14>[ 1556.628138][ T1@C2] init: Seccomp_filters:	0
<14>[ 1556.628144][ T1@C2] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1556.628150][ T1@C2] init: SpeculationIndirectBranch:	unknown
<14>[ 1556.628155][ T1@C2] init: Cpus_allowed:	ff
<14>[ 1556.628161][ T1@C2] init: Cpus_allowed_list:	0-7
<14>[ 1556.628166][ T1@C2] init: Mems_allowed:	1
<14>[ 1556.628172][ T1@C2] init: Mems_allowed_list:	0
<14>[ 1556.628177][ T1@C2] init: voluntary_ctxt_switches:	428
<14>[ 1556.628183][ T1@C2] init: nonvoluntary_ctxt_switches:	180
<14>[ 1556.628189][ T1@C2] init: 
<6>[ 1556.691265][T932@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1556.691347][T932@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1556.691356][T932@C4] [Audio:DSPDUMP] audio_dsp_release
<6>[ 1556.691600][T932@C4] [Audio:DSPDUMP] audio_dsp_release
<14>[ 1556.695529][ T1@C7] init: Still running: 602
<14>[ 1556.695552][ T1@C7] init: Name:	surfaceflinger
<14>[ 1556.695556][ T1@C7] init: State:	Z (zombie)
<14>[ 1556.695559][ T1@C7] init: Tgid:	602
<14>[ 1556.695562][ T1@C7] init: Ngid:	0
<14>[ 1556.695565][ T1@C7] init: Pid:	602
<14>[ 1556.695568][ T1@C7] init: PPid:	1
<14>[ 1556.695571][ T1@C7] init: TracerPid:	0
<14>[ 1556.695574][ T1@C7] init: Uid:	1000	1000	1000	1000
<14>[ 1556.695577][ T1@C7] init: Gid:	1003	1003	1003	1003
<14>[ 1556.695579][ T1@C7] init: FDSize:	0
<14>[ 1556.695582][ T1@C7] init: Groups:	1026 3009 
<14>[ 1556.695585][ T1@C7] init: Threads:	2
<14>[ 1556.695587][ T1@C7] init: SigQ:	3/13409
<14>[ 1556.695590][ T1@C7] init: SigPnd:	0000000000000000
<14>[ 1556.695592][ T1@C7] init: ShdPnd:	0000000000004000
<14>[ 1556.695595][ T1@C7] init: SigBlk:	0000000080000000
<14>[ 1556.695597][ T1@C7] init: SigIgn:	0000002000001000
<14>[ 1556.695600][ T1@C7] init: SigCgt:	0000004c400084f8
<14>[ 1556.695602][ T1@C7] init: CapInh:	0000000000800000
<14>[ 1556.695605][ T1@C7] init: CapPrm:	0000000000800000
<14>[ 1556.695607][ T1@C7] init: CapEff:	0000000000800000
<14>[ 1556.695610][ T1@C7] init: CapBnd:	0000000000800000
<14>[ 1556.695612][ T1@C7] init: CapAmb:	0000000000800000
<14>[ 1556.695615][ T1@C7] init: NoNewPrivs:	0
<14>[ 1556.695617][ T1@C7] init: Seccomp:	0
<14>[ 1556.695620][ T1@C7] init: Seccomp_filters:	0
<14>[ 1556.695623][ T1@C7] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1556.695626][ T1@C7] init: SpeculationIndirectBranch:	unknown
<14>[ 1556.695628][ T1@C7] init: Cpus_allowed:	ff
<14>[ 1556.695631][ T1@C7] init: Cpus_allowed_list:	0-7
<14>[ 1556.695633][ T1@C7] init: Mems_allowed:	1
<14>[ 1556.695636][ T1@C7] init: Mems_allowed_list:	0
<14>[ 1556.695638][ T1@C7] init: voluntary_ctxt_switches:	59885
<14>[ 1556.695641][ T1@C7] init: nonvoluntary_ctxt_switches:	882
<14>[ 1556.695643][ T1@C7] init: 
<14>[ 1556.695929][ T1@C7] init: Still running: 548
<14>[ 1556.695937][ T1@C7] init: Name:	binder:548_2
<14>[ 1556.695940][ T1@C7] init: State:	Z (zombie)
<14>[ 1556.695943][ T1@C7] init: Tgid:	548
<14>[ 1556.695946][ T1@C7] init: Ngid:	0
<14>[ 1556.695948][ T1@C7] init: Pid:	548
<14>[ 1556.695951][ T1@C7] init: PPid:	1
<14>[ 1556.695953][ T1@C7] init: TracerPid:	0
<14>[ 1556.695956][ T1@C7] init: Uid:	1041	1041	1041	1041
<14>[ 1556.695958][ T1@C7] init: Gid:	1005	1005	1005	1005
<14>[ 1556.695961][ T1@C7] init: FDSize:	0
<14>[ 1556.695963][ T1@C7] init: Groups:	1006 1013 1026 1031 1080 ************** 3007 3010 
<14>[ 1556.695966][ T1@C7] init: Threads:	1
<14>[ 1556.695969][ T1@C7] init: SigQ:	1/13409
<14>[ 1556.695971][ T1@C7] init: SigPnd:	0000000000000000
<14>[ 1556.695973][ T1@C7] init: ShdPnd:	0000000000004000
<14>[ 1556.695976][ T1@C7] init: SigBlk:	0000000080000000
<14>[ 1556.695979][ T1@C7] init: SigIgn:	0000002000001000
<14>[ 1556.695981][ T1@C7] init: SigCgt:	0000004c400084f8
<14>[ 1556.695984][ T1@C7] init: CapInh:	0000001000800000
<14>[ 1556.695986][ T1@C7] init: CapPrm:	0000001000800000
<14>[ 1556.695989][ T1@C7] init: CapEff:	0000001000800000
<14>[ 1556.695991][ T1@C7] init: CapBnd:	0000001000800000
<14>[ 1556.695994][ T1@C7] init: CapAmb:	0000001000800000
<14>[ 1556.695996][ T1@C7] init: NoNewPrivs:	0
<14>[ 1556.695999][ T1@C7] init: Seccomp:	0
<14>[ 1556.696001][ T1@C7] init: Seccomp_filters:	0
<14>[ 1556.696004][ T1@C7] init: Speculation_Store_Bypass:	vulnerable
<14>[ 1556.696007][ T1@C7] init: SpeculationIndirectBranch:	unknown
<14>[ 1556.696009][ T1@C7] init: Cpus_allowed:	ff
<14>[ 1556.696012][ T1@C7] init: Cpus_allowed_list:	0-7
<14>[ 1556.696014][ T1@C7] init: Mems_allowed:	1
<14>[ 1556.696017][ T1@C7] init: Mems_allowed_list:	0
<14>[ 1556.696019][ T1@C7] init: voluntary_ctxt_switches:	389
<14>[ 1556.696022][ T1@C7] init: nonvoluntary_ctxt_switches:	461
<14>[ 1556.696024][ T1@C7] init: 
<11>[ 1556.696087][ T1@C7] init: [service-misbehaving] : service 'surfaceflinger' is still running 3000ms after receiving SIGTERM
<11>[ 1556.696099][ T1@C7] init: [service-misbehaving] : service 'update_engine' is still running 3000ms after receiving SIGTERM
<11>[ 1556.696113][ T1@C7] init: [service-misbehaving] : service 'vendor.audio-hal' is still running 3000ms after receiving SIGTERM
<11>[ 1556.696124][ T1@C7] init: [service-misbehaving] : service 'uniber' is still running 3000ms after receiving SIGTERM
<14>[ 1556.696144][ T1@C7] init: Stopping 225 services by sending SIGKILL
<14>[ 1556.696171][ T1@C7] init: Sending signal 9 to service 'update_engine' (pid 791) process group...
<14>[ 1556.831681][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_791
<14>[ 1556.850242][ T1@C4] init: Sending signal 9 to service 'uniber' (pid 747) process group...
<6>[ 1556.881674][T1483@C5] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1556.885580][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_747
<14>[ 1556.896132][ T1@C4] init: Sending signal 9 to service 'surfaceflinger' (pid 602) process group...
<6>[ 1557.120880][T12065@C5] binder: undelivered death notification, b4000077d8c69290
<6>[ 1557.382009][T7636@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[ 1557.779722][T870@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[ 1557.789403][T8169@C1] binder: undelivered transaction 756226, process died.
<14>[ 1557.796455][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_602
<14>[ 1557.797278][ T1@C1] init: Sending signal 9 to service 'vendor.audio-hal' (pid 548) process group...
<14>[ 1557.797674][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_548
<14>[ 1557.798119][ T1@C1] init: Service 'vendor.audio-hal' (pid 548) received signal 15
<14>[ 1557.798142][ T1@C1] init: Sending signal 9 to service 'vendor.audio-hal' (pid 548) process group...
<11>[ 1557.798215][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1041/pid_548/cgroup.kill: No such file or directory
<11>[ 1557.798259][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1041/pid_548/cgroup.procs: No such file or directory
<14>[ 1557.798862][ T1@C1] init: Service 'surfaceflinger' (pid 602) received signal 15
<14>[ 1557.798883][ T1@C1] init: Sending signal 9 to service 'surfaceflinger' (pid 602) process group...
<11>[ 1557.798955][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_602/cgroup.kill: No such file or directory
<11>[ 1557.798995][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_602/cgroup.procs: No such file or directory
<14>[ 1557.799607][ T1@C1] init: Service 'uniber' (pid 747) received signal 9
<14>[ 1557.799629][ T1@C1] init: Sending signal 9 to service 'uniber' (pid 747) process group...
<11>[ 1557.799698][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_747/cgroup.kill: No such file or directory
<11>[ 1557.799738][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_747/cgroup.procs: No such file or directory
<14>[ 1557.800281][ T1@C1] init: Service 'update_engine' (pid 791) received signal 9
<14>[ 1557.800302][ T1@C1] init: Sending signal 9 to service 'update_engine' (pid 791) process group...
<11>[ 1557.800366][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_791/cgroup.kill: No such file or directory
<11>[ 1557.800407][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_791/cgroup.procs: No such file or directory
<14>[ 1557.800969][ T1@C1] init: Untracked pid 1427 received signal 15
<14>[ 1557.800987][ T1@C1] init: Untracked pid 1427 did not have an associated service entry and will not be reaped
<14>[ 1557.801223][ T1@C1] init: Untracked pid 1449 received signal 15
<14>[ 1557.801238][ T1@C1] init: Untracked pid 1449 did not have an associated service entry and will not be reaped
<14>[ 1557.801367][ T1@C1] init: Untracked pid 1472 received signal 15
<14>[ 1557.801381][ T1@C1] init: Untracked pid 1472 did not have an associated service entry and will not be reaped
<14>[ 1557.801480][ T1@C1] init: Untracked pid 1666 received signal 15
<14>[ 1557.801494][ T1@C1] init: Untracked pid 1666 did not have an associated service entry and will not be reaped
<14>[ 1557.801578][ T1@C1] init: Untracked pid 1840 received signal 15
<14>[ 1557.801592][ T1@C1] init: Untracked pid 1840 did not have an associated service entry and will not be reaped
<14>[ 1557.801677][ T1@C1] init: Untracked pid 2237 received signal 15
<14>[ 1557.801691][ T1@C1] init: Untracked pid 2237 did not have an associated service entry and will not be reaped
<14>[ 1557.801801][ T1@C1] init: Untracked pid 2366 received signal 15
<14>[ 1557.801815][ T1@C1] init: Untracked pid 2366 did not have an associated service entry and will not be reaped
<14>[ 1557.801909][ T1@C1] init: Untracked pid 4980 received signal 15
<14>[ 1557.801924][ T1@C1] init: Untracked pid 4980 did not have an associated service entry and will not be reaped
<14>[ 1557.802020][ T1@C1] init: Untracked pid 6516 received signal 15
<14>[ 1557.802034][ T1@C1] init: Untracked pid 6516 did not have an associated service entry and will not be reaped
<14>[ 1557.804112][ T1@C1] init: Untracked pid 6810 received signal 15
<14>[ 1557.804135][ T1@C1] init: Untracked pid 6810 did not have an associated service entry and will not be reaped
<14>[ 1557.804258][ T1@C1] init: Untracked pid 8476 received signal 15
<14>[ 1557.804272][ T1@C1] init: Untracked pid 8476 did not have an associated service entry and will not be reaped
<14>[ 1557.804423][ T1@C1] init: Untracked pid 11620 received signal 15
<14>[ 1557.804438][ T1@C1] init: Untracked pid 11620 did not have an associated service entry and will not be reaped
<14>[ 1557.804859][ T1@C1] init: Calling /system/bin/vdc volume abort_fuse
<15>[ 1558.004749][T12802@C7] vdc: Waited 0ms for vold
<14>[ 1558.064921][ T1@C1] init: Calling /system/bin/vdc volume shutdown
<36>[ 1558.074370][T329@C2] type=1400 audit(1754018603.653:585): avc:  denied  { create } for  comm="binder:337_2" name="abort.tmp" scontext=u:r:vold:s0 tcontext=u:object_r:fusectlfs:s0 tclass=file permissive=0
<15>[ 1558.098281][T12803@C7] vdc: Waited 0ms for vold
<6>[ 1558.148775][T1970@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1558.354421][ T1@C0] init: Sending signal 9 to service 'vold' (pid 337) process group...
<14>[ 1558.485279][ T1@C0] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_337
<14>[ 1558.486342][ T1@C0] init: Stopping 4 services by sending SIGKILL
<14>[ 1558.486482][ T1@C0] init: Sending signal 9 to service 'tombstoned' (pid 452) process group...
<6>[ 1558.508600][T2170@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[ 1558.521449][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1058/pid_452
<14>[ 1558.522404][ T1@C2] init: Sending signal 9 to service 'logd' (pid 302) process group...
<6>[ 1558.538209][T9467@C0] charger-manager charger-manager: vbat: 3758000, vbat_avg: 3757000, OCV: 3782000, ibat: -233000, ibat_avg: -244000, ibus: -22, vbus: 0, msoc: 311, chg_sts: 2, frce_full: 0, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 0, Tboard: 346, Tbatt: 311, thm_cur: 6000000, thm_pwr: 30000, is_fchg: 0, fchg_en: 0, tflush: 1272, tperiod: 16
<6>[ 1558.538336][T9467@C0] charger-manager charger-manager: new_uisoc = 311, old_uisoc = 312, work_cycle = 15s, cap_one_time = 30s
<6>[ 1558.564697][T1407@C0] unisoc_userlog: userlog_release
<14>[ 1558.595081][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1036/pid_302
<14>[ 1558.595991][ T1@C2] init: Subcontext received signal 15
<14>[ 1558.596022][ T1@C2] init: Subcontext did not have an associated service entry and will not be reaped
<14>[ 1558.596206][ T1@C2] init: Service 'vold' (pid 337) received signal 9
<14>[ 1558.596227][ T1@C2] init: Sending signal 9 to service 'vold' (pid 337) process group...
<11>[ 1558.596371][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_337/cgroup.kill: No such file or directory
<11>[ 1558.596417][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_337/cgroup.procs: No such file or directory
<11>[ 1558.596441][ T1@C2] init: Service vold has 'reboot_on_failure' option and failed, shutting down system.
<14>[ 1558.597403][ T1@C2] init: Service 'tombstoned' (pid 452) received signal 9
<14>[ 1558.597431][ T1@C2] init: Sending signal 9 to service 'tombstoned' (pid 452) process group...
<11>[ 1558.597563][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1058/pid_452/cgroup.kill: No such file or directory
<11>[ 1558.597607][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1058/pid_452/cgroup.procs: No such file or directory
<14>[ 1558.599021][ T1@C0] init: Service 'logd' (pid 302) received signal 9
<14>[ 1558.599061][ T1@C0] init: Sending signal 9 to service 'logd' (pid 302) process group...
<11>[ 1558.599189][ T1@C0] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1036/pid_302/cgroup.kill: No such file or directory
<11>[ 1558.599232][ T1@C0] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1036/pid_302/cgroup.procs: No such file or directory
<14>[ 1558.604529][ T1@C2] init: Untracked pid 1426 received signal 15
<14>[ 1558.604574][ T1@C2] init: Untracked pid 1426 did not have an associated service entry and will not be reaped
<14>[ 1558.605448][ T1@C2] init: Untracked pid 1821 received signal 15
<14>[ 1558.605478][ T1@C2] init: Untracked pid 1821 did not have an associated service entry and will not be reaped
<14>[ 1558.605771][ T1@C2] init: Untracked pid 2147 received signal 15
<14>[ 1558.605789][ T1@C2] init: Untracked pid 2147 did not have an associated service entry and will not be reaped
<14>[ 1558.606025][ T1@C2] init: sync() before umount...
<14>[ 1558.626668][ T1@C2] init: sync() before umount took20ms
<14>[ 1558.627904][ T1@C2] init: swapoff() start...
<3>[ 1558.631040][T9467@C0] |__c2e     mmc0: 2039    3    4   24   43    9    0    0    0    0    0    0    0    0
<3>[ 1558.631075][T9467@C0] |__d2e     mmc0:  458    8    4   25   53   15    0    0    0    0    0    0    0    0
<3>[ 1558.631086][T9467@C0] |__blocks  mmc0:    1    0    0    0  462   55   17   19    4    3    2    0    0    0
<3>[ 1558.631096][T9467@C0] |__speed   mmc0: r= 3.27M/s, w= 191.66M/s, r_blk= 7344, w_blk= 2776
<6>[ 1558.709949][T8169@C1] binder: undelivered death notification, b4000077d8c3f230
<6>[ 1558.711832][T8169@C1] binder: undelivered transaction 756227, process died.
<6>[ 1558.712618][T8169@C1] binder: undelivered transaction 756262, process died.
<6>[ 1558.712630][T8169@C1] binder: undelivered transaction 756263, process died.
<6>[ 1558.712635][T8169@C1] binder: undelivered transaction 756264, process died.
<6>[ 1558.722770][T8169@C1] binder: undelivered transaction 756261, process died.
<6>[ 1559.336910][T11606@C3] binder: undelivered transaction 744069, process died.
<6>[ 1559.336951][T11606@C3] binder: undelivered transaction 744640, process died.
<6>[ 1559.336956][T11606@C3] binder: undelivered transaction 752845, process died.
<6>[ 1559.336967][T11606@C3] binder: undelivered transaction 691284, process died.
<14>[ 1560.058814][ T1@C6] init: swapoff() took 1430ms
<6>[ 1560.059283][ T1@C6] zram0: detected capacity change from 4754872 to 0
<6>[ 1560.059643][T10@C5] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[ 1560.059660][T10@C5] WCN BASE: tx:at+loopcheck=1560053,17141
<6>[ 1560.059660][T10@C5] 
<6>[ 1560.059748][T10@C5] WCN BASE: mdbg_tx_cb, chn:0
<6>[ 1560.064437][T383@C5] WCN BASE: : rx:loopcheck_ack:ap_send=1560053,cp2_bootup=17141,cp2_send=5113103
<6>[ 1560.064437][T383@C5] 
<14>[ 1560.083068][ T1@C6] init: Ready to unmount apexes. So far shutdown sequence took 7995ms
<14>[ 1560.174191][T12804@C6] apexd: Started. subcommand = --unmount-all
<14>[ 1560.174462][T12804@C6] apexd-unmount-all: Populating APEX database from mounts...
<14>[ 1560.177873][T12804@C6] apexd-unmount-all: Found "/apex/com.android.wifi@351610000" backed by file /system/apex/com.google.android.wifi.apex
<14>[ 1560.178572][T12804@C6] apexd-unmount-all: Found "/apex/com.android.btservices@352090000" backed by file /system/apex/com.android.btservices.apex
<14>[ 1560.178870][T12804@C6] apexd-unmount-all: Found "/apex/com.android.media.swcodec@351504000" backed by file /system/apex/com.google.android.media.swcodec.apex
<14>[ 1560.179101][T12804@C6] apexd-unmount-all: Found "/apex/com.android.adbd@351010000" backed by file /system/apex/com.google.android.adbd.apex
<14>[ 1560.179344][T12804@C6] apexd-unmount-all: Found "/apex/com.android.nfcservices@352090000" backed by file /system/apex/com.android.nfcservices.apex
<14>[ 1560.179558][T12804@C6] apexd-unmount-all: Found "/apex/com.android.tzdata@351400020" backed by file /system/apex/com.google.android.tzdata6.apex
<14>[ 1560.179761][T12804@C6] apexd-unmount-all: Found "/apex/com.android.virt@2" backed by file /system/apex/com.android.virt.apex
<14>[ 1560.179983][T12804@C6] apexd-unmount-all: Found "/apex/com.android.conscrypt@351412000" backed by file /system/apex/com.google.android.conscrypt.apex
<14>[ 1560.180193][T12804@C6] apexd-unmount-all: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
<14>[ 1560.180392][T12804@C6] apexd-unmount-all: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
<14>[ 1560.180601][T12804@C6] apexd-unmount-all: Found "/apex/com.android.cellbroadcast@351511000" backed by file /system/apex/com.google.android.cellbroadcast.apex
<14>[ 1560.180812][T12804@C6] apexd-unmount-all: Found "/apex/com.android.appsearch@351412000" backed by file /system/apex/com.google.android.appsearch.apex
<14>[ 1560.181010][T12804@C6] apexd-unmount-all: Found "/apex/com.android.neuralnetworks@351010040" backed by file /system/apex/com.google.android.neuralnetworks.apex
<14>[ 1560.181212][T12804@C6] apexd-unmount-all: Found "/apex/com.android.profiling@352090000" backed by file /system/apex/com.android.profiling.apex
<14>[ 1560.181501][T12804@C6] apexd-unmount-all: Found "/apex/com.android.apex.cts.shim@1" backed by file /system/apex/com.android.apex.cts.shim.apex
<14>[ 1560.181824][T12804@C6] apexd-unmount-all: Found "/apex/com.android.configinfrastructure@351010000" backed by file /system/apex/com.google.android.configinfrastructure.apex
<14>[ 1560.181998][T12804@C6] apexd-unmount-all: Found "/apex/com.android.devicelock@342410000" backed by file /system/apex/com.google.android.devicelock.apex
<14>[ 1560.182429][T12804@C6] apexd-unmount-all: Found "/apex/com.android.adservices@351537040" backed by file /system/apex/com.google.android.adservices.apex
<14>[ 1560.182629][T12804@C6] apexd-unmount-all: Found "/apex/com.android.rkpd@351310000" backed by file /system/apex/com.google.android.rkpd.apex
<14>[ 1560.182812][T12804@C6] apexd-unmount-all: Found "/apex/com.android.healthfitness@351511060" backed by file /system/apex/com.google.android.healthfitness.apex
<14>[ 1560.182988][T12804@C6] apexd-unmount-all: Found "/apex/com.android.ipsec@351410000" backed by file /system/apex/com.google.android.ipsec.apex
<14>[ 1560.183161][T12804@C6] apexd-unmount-all: Found "/apex/com.android.media@351504000" backed by file /system/apex/com.google.android.media.apex
<14>[ 1560.183349][T12804@C6] apexd-unmount-all: Found "/apex/com.android.os.statsd@351610000" backed by file /system/apex/com.google.android.os.statsd.apex
<14>[ 1560.183542][T12804@C6] apexd-unmount-all: Found "/apex/com.android.extservices@351538083" backed by file /system/apex/com.google.android.extservices_tplus.apex
<14>[ 1560.183740][T12804@C6] apexd-unmount-all: Found "/apex/com.android.uwb@351310040" backed by file /system/apex/com.google.android.uwb.apex
<14>[ 1560.183918][T12804@C6] apexd-unmount-all: Found "/apex/com.android.vndk.v33@1" backed by file /system_ext/apex/com.android.vndk.v33.apex
<14>[ 1560.184102][T12804@C6] apexd-unmount-all: Found "/apex/com.android.mediaprovider@351613160" backed by file /system/apex/com.google.android.mediaprovider.apex
<14>[ 1560.184281][T12804@C6] apexd-unmount-all: Found "/apex/com.android.art@351610080" backed by file /system/apex/com.google.android.art.apex
<14>[ 1560.184473][T12804@C6] apexd-unmount-all: Found "/apex/com.android.permission@351610020" backed by file /system/apex/com.google.android.permission.apex
<14>[ 1560.184650][T12804@C6] apexd-unmount-all: Found "/apex/com.android.compos@2" backed by file /system_ext/apex/com.android.compos.apex
<14>[ 1560.184833][T12804@C6] apexd-unmount-all: Found "/apex/com.android.sdkext@351415000" backed by file /system/apex/com.google.android.sdkext.apex
<14>[ 1560.185013][T12804@C6] apexd-unmount-all: Found "/apex/com.google.mainline.primary.libs@351165000" backed by file /system/apex/com.google.mainline.primary.libs.apex
<14>[ 1560.185202][T12804@C6] apexd-unmount-all: Found "/apex/com.android.resolv@351510000" backed by file /system/apex/com.google.android.resolv.apex
<14>[ 1560.185448][T12804@C6] apexd-unmount-all: Found "/apex/com.android.ondevicepersonalization@351541000" backed by file /system/apex/com.google.android.ondevicepersonalization.apex
<14>[ 1560.185628][T12804@C6] apexd-unmount-all: Found "/apex/com.android.tethering@351510080" backed by file /system/apex/com.google.android.tethering.apex
<14>[ 1560.185849][T12804@C6] apexd-unmount-all: Found "/apex/com.android.scheduling@351010000" backed by file /system/apex/com.google.android.scheduling.apex
<14>[ 1560.186086][T12804@C6] apexd-unmount-all: 36 packages restored.
<14>[ 1560.186125][T12804@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.adbd.apex mounted on /apex/com.android.adbd@351010000
<14>[ 1560.215986][T12804@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.adservices.apex mounted on /apex/com.android.adservices@351537040
<14>[ 1560.247555][T12804@C6] apexd-unmount-all: Unmounting /system/apex/com.android.apex.cts.shim.apex mounted on /apex/com.android.apex.cts.shim@1
<14>[ 1560.271748][T12804@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.appsearch.apex mounted on /apex/com.android.appsearch@351412000
<14>[ 1560.295974][T12804@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.art.apex mounted on /apex/com.android.art@351610080
<14>[ 1560.343794][T12804@C1] apexd-unmount-all: Unmounting /system/apex/com.android.btservices.apex mounted on /apex/com.android.btservices@352090000
<14>[ 1560.380730][T12804@C1] apexd-unmount-all: Unmounting /system/apex/com.google.android.cellbroadcast.apex mounted on /apex/com.android.cellbroadcast@351511000
<14>[ 1560.420377][T12804@C1] apexd-unmount-all: Unmounting /system_ext/apex/com.android.compos.apex mounted on /apex/com.android.compos@2
<14>[ 1560.452144][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.configinfrastructure.apex mounted on /apex/com.android.configinfrastructure@351010000
<14>[ 1560.484283][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.conscrypt.apex mounted on /apex/com.android.conscrypt@351412000
<14>[ 1560.515992][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.devicelock.apex mounted on /apex/com.android.devicelock@342410000
<14>[ 1560.552099][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.extservices_tplus.apex mounted on /apex/com.android.extservices@351538083
<14>[ 1560.579646][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.healthfitness.apex mounted on /apex/com.android.healthfitness@351511060
<14>[ 1560.604944][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.android.i18n.apex mounted on /apex/com.android.i18n@1
<14>[ 1560.648346][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.ipsec.apex mounted on /apex/com.android.ipsec@351410000
<14>[ 1560.663979][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.apex mounted on /apex/com.android.media@351504000
<14>[ 1560.693136][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.swcodec.apex mounted on /apex/com.android.media.swcodec@351504000
<14>[ 1560.733038][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.mediaprovider.apex mounted on /apex/com.android.mediaprovider@351613160
<14>[ 1560.755881][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.neuralnetworks.apex mounted on /apex/com.android.neuralnetworks@351010040
<14>[ 1560.792791][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.android.nfcservices.apex mounted on /apex/com.android.nfcservices@352090000
<14>[ 1560.828180][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.ondevicepersonalization.apex mounted on /apex/com.android.ondevicepersonalization@351541000
<14>[ 1560.852066][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.os.statsd.apex mounted on /apex/com.android.os.statsd@351610000
<14>[ 1560.884468][T12804@C3] apexd-unmount-all: Unmounting /system/apex/com.google.android.permission.apex mounted on /apex/com.android.permission@351610020
<14>[ 1560.916211][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.android.profiling.apex mounted on /apex/com.android.profiling@352090000
<14>[ 1560.939635][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.resolv.apex mounted on /apex/com.android.resolv@351510000
<14>[ 1560.964128][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.rkpd.apex mounted on /apex/com.android.rkpd@351310000
<14>[ 1560.988059][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.android.runtime.apex mounted on /apex/com.android.runtime@1
<11>[ 1560.989071][T12804@C2] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.runtime: Device or resource busy
<14>[ 1560.989116][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.scheduling.apex mounted on /apex/com.android.scheduling@351010000
<14>[ 1561.012087][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.sdkext.apex mounted on /apex/com.android.sdkext@351415000
<14>[ 1561.044672][T12804@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.tethering.apex mounted on /apex/com.android.tethering@351510080
<14>[ 1561.079836][T12804@C5] apexd-unmount-all: Unmounting /system/apex/com.google.android.tzdata6.apex mounted on /apex/com.android.tzdata@351400020
<14>[ 1561.104377][T12804@C5] apexd-unmount-all: Unmounting /system/apex/com.google.android.uwb.apex mounted on /apex/com.android.uwb@351310040
<14>[ 1561.131254][T12804@C5] apexd-unmount-all: Unmounting /system/apex/com.android.virt.apex mounted on /apex/com.android.virt@2
<14>[ 1561.163954][T12804@C0] apexd-unmount-all: Unmounting /system_ext/apex/com.android.vndk.v33.apex mounted on /apex/com.android.vndk.v33@1
<11>[ 1561.164842][T12804@C0] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.vndk.v33: Device or resource busy
<14>[ 1561.164881][T12804@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.wifi.apex mounted on /apex/com.android.wifi@351610000
<14>[ 1561.196219][T12804@C1] apexd-unmount-all: Unmounting /system/apex/com.google.mainline.primary.libs.apex mounted on /apex/com.google.mainline.primary.libs@351165000
<14>[ 1561.229885][ T1@C6] apexd: apexd terminated by exit(1)
<14>[ 1561.229885][ T1@C6] 
<11>[ 1561.230191][ T1@C6] init: '/system/bin/apexd --unmount-all' failed : 256
<14>[ 1561.243283][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.244431][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.244459][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.244828][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.244842][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.245232][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.245244][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246084][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246126][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246557][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246571][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246955][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.246968][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.247044][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.247057][ T1@C6] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.247281][ T1@C6] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1561.247320][ T1@C6] init: Unmounting /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<3>[ 1562.358734][T11606@C3] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=265892,calib_resistance_vol=-108,vol_adc_mv=266
<6>[ 1562.359446][T11606@C3] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 16365773, start_work_clbcnt = 16365773, cur_clbcnt = 14985741, cur_1000ma_adc = 1366, vol_1000mv_adc = 683, calib_resist = 10000
<6>[ 1562.359481][T11606@C3] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 331, init_mah = 0, normal_cap = 303, data->cc_mah = -140, Tbat = 311, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[ 1562.359506][T11606@C3] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 3783000, vbatt_mv = 3772, vbat_cur_ma = -108, vbat_avg_mv = 3767, vbat_cur_avg_ma = -158, absolute_charger_mode = 0, full_percent = 0
<6>[ 1562.359530][T11606@C3] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 311, cycle = 15
<14>[ 1562.917163][ T1@C6] init: Umounted /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[ 1562.917207][ T1@C6] init: Unmounting /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.939318][ T1@C7] init: Umounted /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.939356][ T1@C7] init: Unmounting /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.955123][ T1@C6] init: Umounted /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.955179][ T1@C6] init: Unmounting /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[ 1562.964882][ T1@C7] init: Umounted /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[ 1562.964921][ T1@C7] init: Unmounting /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.987587][ T1@C6] init: Umounted /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[ 1562.987646][ T1@C6] init: sync() after umount...
<14>[ 1562.987892][ T1@C6] init: sync() after umount took0ms
<12>[ 1563.088687][ T1@C6] init: powerctl_shutdown_time_ms:11001:0
<14>[ 1563.089212][T12796@C4] init: remaining_shutdown_time: 295
<3>[ 1563.089475][ T1@C6] shutdown_detect_check: shutdown_detect_phase: shutdown  current phase systemcall
<14>[ 1563.089498][ T1@C6] init: Reboot ending, jumping to kernel
<3>[ 1563.089614][ T1@C6] failed to open '/dev/block/by-name/sd_klog':-2!


