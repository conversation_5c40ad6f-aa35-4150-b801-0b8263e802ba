phoneinfo start


cat /proc/cmdline
Fri Aug  1 12:01:53 CST 2025
stack_depot_disable=on kasan.stacktrace=off kvm-arm.mode=protected cgroup_disable=pressure earlycon console=ttyS1,921600n8
       loop.max_part=7 loglevel=1 log_buf_len=1M kpti=0
       firmware_class.path=/odm/firmware,/vendor/firmware
       init=/init root=/dev/ram0 rw printk.devkmsg=on ftrace_dump_on_oops
       swiotlb=1 dummy_hcd.num=0 rcupdate.rcu_expedited=1 rcu_nocbs=0-7 kvm-arm.mode=none lcd_id=ID77666 lcd_name=lcd_ft8057m_sharp_dzx_mipi lcd_base=9caa8000 lcd_size=1600x720 logo_bpix=24  sysdump_magic=80001000 sysdump_re_flag=1  sprdboot.usbmux=0x0 modem=shutdown sprdboot.mode=normal ltemode=lcsfb rfboard.id=0 rfhw.id=41008 crystal=6 32k.less=1 marlin.clktype=1 power.from.extern=0 cpcmdline=end  bootconfig bootcause="Reboot into normal" pwroffcause="device power down" charge.shutdown_rtc_time=1754018588  charge.charge_cycle=1342  charge.basp=-1  charge.total_mah=5028000   sprdboot.wdten=e551  sprdboot.dswdten=enabled  sprdboot.dvfs_set=0x0,0,0 sprdboot.slot_suffix=_a FlashID=444134323536 sprdboot.flash=emmc  ro.product.name=A601N 


cat /proc/version
Fri Aug  1 12:01:53 CST 2025
Linux version 5.15.149-android13-8-00008-gbe074b05e5af-ab12096863 (build-user@build-host) (Android (8508608, based on r450784e) clang version 14.0.7 (https://android.googlesource.com/toolchain/llvm-project 4c603efb0cca074e9238af8b4106c30add4418f6), LLD 14.0.7) #1 SMP PREEMPT Tue Jul 16 15:36:08 UTC 2024


cat /proc/meminfo
Fri Aug  1 12:01:53 CST 2025
MemTotal:        3834576 kB
MemFree:          113288 kB
MemAvailable:    2003144 kB
Buffers:           17508 kB
Cached:          2009324 kB
SwapCached:        69004 kB
Active:           792496 kB
Inactive:        2023708 kB
Active(anon):     184880 kB
Inactive(anon):   626228 kB
Active(file):     607616 kB
Inactive(file):  1397480 kB
Unevictable:       18652 kB
Mlocked:           18652 kB
SwapTotal:       2377432 kB
SwapFree:        1761608 kB
Dirty:              6820 kB
Writeback:             0 kB
AnonPages:        796584 kB
Mapped:          1118956 kB
Shmem:              9932 kB
KReclaimable:     130060 kB
Slab:             285592 kB
SReclaimable:      95244 kB
SUnreclaim:       190348 kB
KernelStack:       43824 kB
ShadowCallStack:   10992 kB
PageTables:        92416 kB
NFS_Unstable:          0 kB
Bounce:                0 kB
WritebackTmp:          0 kB
CommitLimit:     4294720 kB
Committed_AS:   159953500 kB
VmallocTotal:   259653632 kB
VmallocUsed:      132632 kB
VmallocChunk:          0 kB
Percpu:             8736 kB
AnonHugePages:         0 kB
ShmemHugePages:        0 kB
ShmemPmdMapped:        0 kB
FileHugePages:         0 kB
FilePmdMapped:         0 kB
CmaTotal:         344064 kB
CmaFree:            5516 kB


cat /proc/slabinfo
Fri Aug  1 12:01:53 CST 2025
slabinfo - version: 2.1
# name            <active_objs> <num_objs> <objsize> <objperslab> <pagesperslab> : tunables <limit> <batchcount> <sharedfactor> : slabdata <active_slabs> <num_slabs> <sharedavail>
scsi_sense_cache      32     96    128   32    1 : tunables    0    0    0 : slabdata      3      3      0
zspage             12285  19040     48   85    1 : tunables    0    0    0 : slabdata    224    224      0
zs_handle          61556 100352      8  512    1 : tunables    0    0    0 : slabdata    196    196      0
f2fs_page_array_entry-254:50      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-254:50    312    312    208   39    2 : tunables    0    0    0 : slabdata      8      8      0
imsbr_flow            64    102     80   51    1 : tunables    0    0    0 : slabdata      2      2      0
bio_fallback_crypt_ctx    748    748    120   34    1 : tunables    0    0    0 : slabdata     22     22      0
va-region-slab-mali0   1440   1440    168   24    1 : tunables    0    0    0 : slabdata     60     60      0
f2fs_page_array_entry-259:41      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:41     78    195    208   39    2 : tunables    0    0    0 : slabdata      5      5      0
f2fs_page_array_entry-259:40      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:40     39    156    208   39    2 : tunables    0    0    0 : slabdata      4      4      0
ext4_groupinfo_4k     73    296    216   37    2 : tunables    0    0    0 : slabdata      8      8      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4      2     25    160   25    1 : tunables    0    0    0 : slabdata      1      1      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4     28     50    160   25    1 : tunables    0    0    0 : slabdata      2      2      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    790    825    160   25    1 : tunables    0    0    0 : slabdata     33     33      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4     89    200    160   25    1 : tunables    0    0    0 : slabdata      8      8      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    142    375    160   25    1 : tunables    0    0    0 : slabdata     15     15      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    171    325    160   25    1 : tunables    0    0    0 : slabdata     13     13      0
bio-373              112    128   3840    8    8 : tunables    0    0    0 : slabdata     16     16      0
bio-380              232    232   3840    8    8 : tunables    0    0    0 : slabdata     29     29      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer        1     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    419    475    160   25    1 : tunables    0    0    0 : slabdata     19     19      0
bio-208              256    416    256   32    2 : tunables    0    0    0 : slabdata     13     13      0
bio-272              336    336    384   21    2 : tunables    0    0    0 : slabdata     16     16      0
f2fs_page_array_entry-259:44      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:44     39    234    208   39    2 : tunables    0    0    0 : slabdata      6      6      0
fsverity_info        154    256    256   32    2 : tunables    0    0    0 : slabdata      8      8      0
fscrypt_info        4674   5550    136   30    1 : tunables    0    0    0 : slabdata    185    185      0
wakeup_irq_node_cache    128    128     32  128    1 : tunables    0    0    0 : slabdata      1      1      0
AF_VSOCK               0      0   1472   22    8 : tunables    0    0    0 : slabdata      0      0      0
lowpan-frags           0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
IEEE-802.15.4-MAC      0      0    960   34    8 : tunables    0    0    0 : slabdata      0      0      0
IEEE-802.15.4-RAW      0      0    896   36    8 : tunables    0    0    0 : slabdata      0      0      0
p9_req_t               0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
TIPC                   1     28   1152   28    8 : tunables    0    0    0 : slabdata      1      1      0
can_gw                 0      0    824   39    8 : tunables    0    0    0 : slabdata      0      0      0
can_receiver           0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
bridge_fdb_cache       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
nf-frags               0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
xfrm6_tunnel_spi       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
ip6-frags              0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
fib6_nodes            37     64    128   32    1 : tunables    0    0    0 : slabdata      2      2      0
ip6_dst_cache         32     64    256   32    2 : tunables    0    0    0 : slabdata      2      2      0
PINGv6                 0      0   1280   25    8 : tunables    0    0    0 : slabdata      0      0      0
RAWv6                125    150   1280   25    8 : tunables    0    0    0 : slabdata      6      6      0
UDPLITEv6              0      0   1408   23    8 : tunables    0    0    0 : slabdata      0      0      0
UDPv6                 70    138   1408   23    8 : tunables    0    0    0 : slabdata      6      6      0
tw_sock_TCPv6          0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
request_sock_TCPv6      0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
TCPv6                  0     12   2560   12    8 : tunables    0    0    0 : slabdata      1      1      0
xt_hashlimit           0      0    120   34    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conncount_rb        0      0     96   42    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conncount_tuple      0      0     72   56    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conntrack_expect      0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
nf_conntrack          27    100    320   25    2 : tunables    0    0    0 : slabdata      4      4      0
fq_flow_cache          0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
ashmem_range_cache    512    512     64   64    1 : tunables    0    0    0 : slabdata      8      8      0
ashmem_area_cache    164    208    312   26    2 : tunables    0    0    0 : slabdata      8      8      0
dm_snap_pending_exception      0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
dm_exception           0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
kcopyd_job             0      0   3384    9    8 : tunables    0    0    0 : slabdata      0      0      0
io                   336    384     64   64    1 : tunables    0    0    0 : slabdata      6      6      0
dm_uevent              0      0   2888   11    8 : tunables    0    0    0 : slabdata      0      0      0
wg_peer                0      0   1760   18    8 : tunables    0    0    0 : slabdata      0      0      0
allowedips_node        0      0     72   56    1 : tunables    0    0    0 : slabdata      0      0      0
sd_ext_cdb             2    128     32  128    1 : tunables    0    0    0 : slabdata      1      1      0
bio-160              248    416    256   32    2 : tunables    0    0    0 : slabdata     13     13      0
sgpool-128            10     32   4096    8    8 : tunables    0    0    0 : slabdata      4      4      0
sgpool-64             16     16   2048   16    8 : tunables    0    0    0 : slabdata      1      1      0
sgpool-32              2     96   1024   32    8 : tunables    0    0    0 : slabdata      3      3      0
sgpool-16             32     96    512   32    4 : tunables    0    0    0 : slabdata      3      3      0
sgpool-8               2    160    256   32    2 : tunables    0    0    0 : slabdata      5      5      0
io_kiocb               0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
bfq_io_cq              0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
bfq_queue              0      0    560   29    4 : tunables    0    0    0 : slabdata      0      0      0
bio-248                4     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
erofs_pcluster-256      0      0   2160   15    8 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-128      0      0   1136   28    8 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-64      0      0    624   26    4 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-16      0      0    240   34    2 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-4       0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-1   12922  20026    120   34    1 : tunables    0    0    0 : slabdata    589    589      0
erofs_inode         4780   4780    808   20    4 : tunables    0    0    0 : slabdata    239    239      0
f2fs_casefolded_name    192    192    256   32    2 : tunables    0    0    0 : slabdata      6      6      0
f2fs_dic_entry         0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
f2fs_cic_entry         0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_bio_entry_slab    850   1020     24  170    1 : tunables    0    0    0 : slabdata      6      6      0
f2fs_bio_iostat_ctx   1536   1536     32  128    1 : tunables    0    0    0 : slabdata     12     12      0
f2fs_bio_post_read_ctx    184    504     72   56    1 : tunables    0    0    0 : slabdata      9      9      0
f2fs_victim_entry      0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_extent_node     896    960     64   64    1 : tunables    0    0    0 : slabdata     15     15      0
f2fs_extent_tree    3009   3009     80   51    1 : tunables    0    0    0 : slabdata     59     59      0
f2fs_fsync_inode_entry      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_inode_entry       0    340     24  170    1 : tunables    0    0    0 : slabdata      2      2      0
f2fs_ino_entry      1360   1360     24  170    1 : tunables    0    0    0 : slabdata      8      8      0
f2fs_revoke_entry    896    896     32  128    1 : tunables    0    0    0 : slabdata      7      7      0
f2fs_sit_entry_set   1020   1020     24  170    1 : tunables    0    0    0 : slabdata      6      6      0
f2fs_discard_cmd    2160   2160    112   36    1 : tunables    0    0    0 : slabdata     60     60      0
f2fs_discard_entry    322    322     88   46    1 : tunables    0    0    0 : slabdata      7      7      0
f2fs_fsync_node_entry   1024   1024     32  128    1 : tunables    0    0    0 : slabdata      8      8      0
f2fs_nat_entry_set    816    816     40  102    1 : tunables    0    0    0 : slabdata      8      8      0
f2fs_free_nid      13951  14280     24  170    1 : tunables    0    0    0 : slabdata     84     84      0
f2fs_nat_entry      4539   4992     32  128    1 : tunables    0    0    0 : slabdata     39     39      0
f2fs_inode_cache    5301   5658   1424   23    8 : tunables    0    0    0 : slabdata    246    246      0
ovl_aio_req            0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
ovl_inode              0      0    864   37    8 : tunables    0    0    0 : slabdata      0      0      0
fuse_bpf_aio_req       0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
fuse_request          78    156    152   26    1 : tunables    0    0    0 : slabdata      6      6      0
fuse_inode            41    192   1024   32    8 : tunables    0    0    0 : slabdata      6      6      0
exfat_inode_cache     33     99    984   33    8 : tunables    0    0    0 : slabdata      3      3      0
exfat_cache            0      0     40  102    1 : tunables    0    0    0 : slabdata      0      0      0
fat_inode_cache        0      0    920   35    8 : tunables    0    0    0 : slabdata      0      0      0
fat_cache              0      0     40  102    1 : tunables    0    0    0 : slabdata      0      0      0
jbd2_transaction_s     33     64    256   32    2 : tunables    0    0    0 : slabdata      2      2      0
jbd2_inode             2    192     64   64    1 : tunables    0    0    0 : slabdata      3      3      0
jbd2_journal_handle     73    438     56   73    1 : tunables    0    0    0 : slabdata      6      6      0
jbd2_journal_head     39    102    120   34    1 : tunables    0    0    0 : slabdata      3      3      0
jbd2_revoke_table_s      2    256     16  256    1 : tunables    0    0    0 : slabdata      1      1      0
jbd2_revoke_record_s      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_fc_dentry_update      0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_inode_cache    1048   1127   1376   23    8 : tunables    0    0    0 : slabdata     49     49      0
ext4_free_data         0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_allocation_context      0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_prealloc_space      0      0    104   39    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_system_zone     139    816     40  102    1 : tunables    0    0    0 : slabdata      8      8      0
ext4_io_end_vec        0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_io_end            0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_bio_post_read_ctx    128    128     64   64    1 : tunables    0    0    0 : slabdata      2      2      0
ext4_pending_reservation      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_extent_status   2068   2652     40  102    1 : tunables    0    0    0 : slabdata     26     26      0
mbcache                0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
kioctx                50     50    640   25    4 : tunables    0    0    0 : slabdata      2      2      0
aio_kiocb              0      0    192   21    1 : tunables    0    0    0 : slabdata      0      0      0
userfaultfd_ctx_cache    173    189    192   21    1 : tunables    0    0    0 : slabdata      9      9      0
dio                    0      0    640   25    4 : tunables    0    0    0 : slabdata      0      0      0
fasync_cache           0      0     48   85    1 : tunables    0    0    0 : slabdata      0      0      0
audit_tree_mark        0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
posix_timers_cache     59    217    264   31    2 : tunables    0    0    0 : slabdata      7      7      0
UNIX-STREAM          260    260   1216   26    8 : tunables    0    0    0 : slabdata     10     10      0
UNIX                 494    494   1216   26    8 : tunables    0    0    0 : slabdata     19     19      0
ip4-frags              0      0    216   37    2 : tunables    0    0    0 : slabdata      0      0      0
UDP-Lite               0      0   1216   26    8 : tunables    0    0    0 : slabdata      0      0      0
tcp_bind_bucket       39    192    128   32    1 : tunables    0    0    0 : slabdata      6      6      0
inet_peer_cache        0      0    192   21    1 : tunables    0    0    0 : slabdata      0      0      0
xfrm_dst_cache         0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
xfrm_state             0      0    768   21    4 : tunables    0    0    0 : slabdata      0      0      0
ip_fib_trie            4     85     48   85    1 : tunables    0    0    0 : slabdata      1      1      0
ip_fib_alias           4     73     56   73    1 : tunables    0    0    0 : slabdata      1      1      0
ip_dst_cache          24     84    192   21    1 : tunables    0    0    0 : slabdata      4      4      0
PING                   0      0   1088   30    8 : tunables    0    0    0 : slabdata      0      0      0
RAW                  180    180   1088   30    8 : tunables    0    0    0 : slabdata      6      6      0
UDP                  131    182   1216   26    8 : tunables    0    0    0 : slabdata      7      7      0
tw_sock_TCP            0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
request_sock_TCP      25     75    320   25    2 : tunables    0    0    0 : slabdata      3      3      0
TCP                   22     65   2368   13    8 : tunables    0    0    0 : slabdata      5      5      0
dquot               1746   1952    256   32    2 : tunables    0    0    0 : slabdata     61     61      0
bio-280               32     42    384   21    2 : tunables    0    0    0 : slabdata      2      2      0
ep_head             2048   2048     16  256    1 : tunables    0    0    0 : slabdata      8      8      0
eventpoll_pwq       1019   1024     64   64    1 : tunables    0    0    0 : slabdata     16     16      0
eventpoll_epi        928    928    128   32    1 : tunables    0    0    0 : slabdata     29     29      0
inotify_inode_mark     68    357     80   51    1 : tunables    0    0    0 : slabdata      7      7      0
dax_cache             48    204    960   34    8 : tunables    0    0    0 : slabdata      6      6      0
bio_crypt_ctx        918    918     40  102    1 : tunables    0    0    0 : slabdata      9      9      0
request_queue         90    154   2200   14    8 : tunables    0    0    0 : slabdata     11     11      0
blkdev_ioc          1677   1677    104   39    1 : tunables    0    0    0 : slabdata     43     43      0
bio-224              608    608    256   32    2 : tunables    0    0    0 : slabdata     19     19      0
biovec-max           496    512   4096    8    8 : tunables    0    0    0 : slabdata     64     64      0
biovec-128            32     96   2048   16    8 : tunables    0    0    0 : slabdata      6      6      0
biovec-64            256    256   1024   32    8 : tunables    0    0    0 : slabdata      8      8      0
biovec-16            256    256    256   32    2 : tunables    0    0    0 : slabdata      8      8      0
khugepaged_mm_slot      0      0    112   36    1 : tunables    0    0    0 : slabdata      0      0      0
uid_cache            157    168    192   21    1 : tunables    0    0    0 : slabdata      8      8      0
iommu_iova             0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
dmaengine-unmap-2      1     64     64   64    1 : tunables    0    0    0 : slabdata      1      1      0
audit_buffer        1360   1360     24  170    1 : tunables    0    0    0 : slabdata      8      8      0
sock_inode_cache     748    748    960   34    8 : tunables    0    0    0 : slabdata     22     22      0
skbuff_ext_cache       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
skbuff_fclone_cache     32    192    512   32    4 : tunables    0    0    0 : slabdata      6      6      0
skbuff_head_cache   2464   2464    256   32    2 : tunables    0    0    0 : slabdata     77     77      0
configfs_dir_cache    113    322     88   46    1 : tunables    0    0    0 : slabdata      7      7      0
file_lock_cache      297    297    248   33    2 : tunables    0    0    0 : slabdata      9      9      0
file_lock_ctx        533    584     56   73    1 : tunables    0    0    0 : slabdata      8      8      0
fsnotify_mark_connector    142    768     32  128    1 : tunables    0    0    0 : slabdata      6      6      0
buffer_head         2278   2340    104   39    1 : tunables    0    0    0 : slabdata     60     60      0
taskstats            133    184    352   23    2 : tunables    0    0    0 : slabdata      8      8      0
proc_dir_entry      1172   1260    192   21    1 : tunables    0    0    0 : slabdata     60     60      0
pde_opener           816    816     40  102    1 : tunables    0    0    0 : slabdata      8      8      0
proc_inode_cache    2091   3003    832   39    8 : tunables    0    0    0 : slabdata     77     77      0
seq_file             270    270    136   30    1 : tunables    0    0    0 : slabdata      9      9      0
sigqueue             408    408     80   51    1 : tunables    0    0    0 : slabdata      8      8      0
bdev_cache           168    255   1920   17    8 : tunables    0    0    0 : slabdata     15     15      0
shmem_inode_cache   2405   2405    864   37    8 : tunables    0    0    0 : slabdata     65     65      0
kernfs_iattrs_cache   3933   4416     88   46    1 : tunables    0    0    0 : slabdata     96     96      0
kernfs_node_cache  62628  63030    136   30    1 : tunables    0    0    0 : slabdata   2101   2101      0
mnt_cache           7392   7497    384   21    2 : tunables    0    0    0 : slabdata    357    357      0
filp               21044  21225    320   25    2 : tunables    0    0    0 : slabdata    849    849      0
inode_cache        45753  47838    760   21    4 : tunables    0    0    0 : slabdata   2278   2278      0
dentry             63165  76323    208   39    2 : tunables    0    0    0 : slabdata   1957   1957      0
names_cache           64     64   4096    8    8 : tunables    0    0    0 : slabdata      8      8      0
net_namespace          0      0   3968    8    8 : tunables    0    0    0 : slabdata      0      0      0
hashtab_node        4299   4420     24  170    1 : tunables    0    0    0 : slabdata     26     26      0
ebitmap_node        8064   8064     64   64    1 : tunables    0    0    0 : slabdata    126    126      0
avtab_extended_perms    825    918     40  102    1 : tunables    0    0    0 : slabdata      9      9      0
avtab_node         53121  53210     24  170    1 : tunables    0    0    0 : slabdata    313    313      0
avc_xperms_data     1024   1024     32  128    1 : tunables    0    0    0 : slabdata      8      8      0
avc_xperms_decision_node    680    680     48   85    1 : tunables    0    0    0 : slabdata      8      8      0
avc_xperms_node     1606   1606     56   73    1 : tunables    0    0    0 : slabdata     22     22      0
avc_node            4816   4816     72   56    1 : tunables    0    0    0 : slabdata     86     86      0
iint_cache             0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
lsm_inode_cache    61901  73073     56   73    1 : tunables    0    0    0 : slabdata   1001   1001      0
lsm_file_cache     24579  25600     16  256    1 : tunables    0    0    0 : slabdata    100    100      0
key_jar               47     96    256   32    2 : tunables    0    0    0 : slabdata      3      3      0
uts_namespace          0      0    432   37    4 : tunables    0    0    0 : slabdata      0      0      0
nsproxy              356    448     72   56    1 : tunables    0    0    0 : slabdata      8      8      0
vm_area_struct    179490 180215    232   35    2 : tunables    0    0    0 : slabdata   5149   5149      0
fs_cache             468    576     64   64    1 : tunables    0    0    0 : slabdata      9      9      0
files_cache          247    276    704   23    4 : tunables    0    0    0 : slabdata     12     12      0
signal_cache         741    784   1152   28    8 : tunables    0    0    0 : slabdata     28     28      0
sighand_cache        708    750   2112   15    8 : tunables    0    0    0 : slabdata     50     50      0
task_struct         2765   2891   4608    7    8 : tunables    0    0    0 : slabdata    413    413      0
cred_jar            1363   1617    192   21    1 : tunables    0    0    0 : slabdata     77     77      0
anon_vma_chain    106793 107008     64   64    1 : tunables    0    0    0 : slabdata   1672   1672      0
anon_vma           61054  61312    128   32    1 : tunables    0    0    0 : slabdata   1916   1916      0
pid                 2906   3392    128   32    1 : tunables    0    0    0 : slabdata    106    106      0
perf_event            40    155   1040   31    8 : tunables    0    0    0 : slabdata      5      5      0
trace_event_file    4019   4140     88   46    1 : tunables    0    0    0 : slabdata     90     90      0
ftrace_event_field   4536   4760     48   85    1 : tunables    0    0    0 : slabdata     56     56      0
pool_workqueue       215    256    256   32    2 : tunables    0    0    0 : slabdata      8      8      0
radix_tree_node    33141  33544    584   28    4 : tunables    0    0    0 : slabdata   1198   1198      0
task_group            35     96    512   32    4 : tunables    0    0    0 : slabdata      3      3      0
mm_struct            277    288   1024   32    8 : tunables    0    0    0 : slabdata      9      9      0
vmap_area          10027  10816     64   64    1 : tunables    0    0    0 : slabdata    169    169      0
kmalloc-rcl-8k         0      0   8192    4    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-4k         0      0   4096    8    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-2k         0      0   2048   16    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-1k         0      0   1024   32    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-512        0      0    512   32    4 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-256       57    192    256   32    2 : tunables    0    0    0 : slabdata      6      6      0
kmalloc-rcl-128     2542   4064    128   32    1 : tunables    0    0    0 : slabdata    127    127      0
kmalloc-cg-8k         26     32   8192    4    8 : tunables    0    0    0 : slabdata      8      8      0
kmalloc-cg-4k        149    160   4096    8    8 : tunables    0    0    0 : slabdata     20     20      0
kmalloc-cg-2k        224    224   2048   16    8 : tunables    0    0    0 : slabdata     14     14      0
kmalloc-cg-1k        836    896   1024   32    8 : tunables    0    0    0 : slabdata     28     28      0
kmalloc-cg-512      1856   1856    512   32    4 : tunables    0    0    0 : slabdata     58     58      0
kmalloc-cg-256       382    384    256   32    2 : tunables    0    0    0 : slabdata     12     12      0
kmalloc-cg-128      9120   9120    128   32    1 : tunables    0    0    0 : slabdata    285    285      0
kmalloc-8k           329    332   8192    4    8 : tunables    0    0    0 : slabdata     83     83      0
kmalloc-4k          1493   1520   4096    8    8 : tunables    0    0    0 : slabdata    190    190      0
kmalloc-2k          1568   1568   2048   16    8 : tunables    0    0    0 : slabdata     98     98      0
kmalloc-1k          2262   2304   1024   32    8 : tunables    0    0    0 : slabdata     72     72      0
kmalloc-512        12447  12672    512   32    4 : tunables    0    0    0 : slabdata    396    396      0
kmalloc-256        55623  55744    256   32    2 : tunables    0    0    0 : slabdata   1742   1742      0
kmalloc-128       206792 217216    128   32    1 : tunables    0    0    0 : slabdata   6788   6788      0
kmem_cache_node      299    448    128   32    1 : tunables    0    0    0 : slabdata     14     14      0
kmem_cache           287    378    384   21    2 : tunables    0    0    0 : slabdata     18     18      0


cat /proc/mounts
Fri Aug  1 12:01:53 CST 2025
/dev/block/dm-7 / erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
tmpfs /dev tmpfs rw,seclabel,nosuid,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
devpts /dev/pts devpts rw,seclabel,relatime,mode=600,ptmxmode=000 0 0
proc /proc proc rw,relatime,gid=3009,hidepid=invisible 0 0
sysfs /sys sysfs rw,seclabel,relatime 0 0
selinuxfs /sys/fs/selinux selinuxfs rw,relatime 0 0
tmpfs /mnt tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755,gid=1000 0 0
tmpfs /mnt/installer tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755,gid=1000 0 0
tmpfs /mnt/androidwritable tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755,gid=1000 0 0
/dev/block/mmcblk0p51 /metadata f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
/dev/block/dm-8 /system_ext erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-9 /vendor erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-10 /odm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-11 /product erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-12 /vendor_dlkm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-13 /system_dlkm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
tmpfs /apex tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
tmpfs /bootstrap-apex tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
tmpfs /linkerconfig tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
none /dev/blkio cgroup rw,nosuid,nodev,noexec,relatime,blkio 0 0
none /sys/fs/cgroup cgroup2 rw,nosuid,nodev,noexec,relatime,memory_recursiveprot 0 0
none /dev/cpuctl cgroup rw,nosuid,nodev,noexec,relatime,cpu 0 0
none /dev/cpuset cgroup rw,nosuid,nodev,noexec,relatime,cpuset,noprefix,release_agent=/sbin/cpuset_release_agent 0 0
none /dev/memcg cgroup rw,nosuid,nodev,noexec,relatime,memory 0 0
/dev/block/loop2 /bootstrap-apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop1 /bootstrap-apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop3 /bootstrap-apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop0 /bootstrap-apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop3 /bootstrap-apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop2 /bootstrap-apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop0 /bootstrap-apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop1 /bootstrap-apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
tmpfs /bootstrap-apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
tracefs /sys/kernel/tracing tracefs rw,seclabel,relatime,gid=3012 0 0
tmpfs /tmp tmpfs rw,seclabel,relatime,size=1907128k,nr_inodes=476782 0 0
none /config configfs rw,nosuid,nodev,noexec,relatime 0 0
binder /dev/binderfs binder rw,relatime,max=1048576,stats=global 0 0
none /sys/fs/fuse/connections fusectl rw,relatime 0 0
bpf /sys/fs/bpf bpf rw,nosuid,nodev,noexec,relatime 0 0
pstore /sys/fs/pstore pstore rw,seclabel,nosuid,nodev,noexec,relatime 0 0
adb /dev/usb-ffs/adb functionfs rw,relatime 0 0
mtp /dev/usb-ffs/mtp functionfs rw,relatime 0 0
ptp /dev/usb-ffs/ptp functionfs rw,relatime 0 0
/dev/block/mmcblk0p1 /mnt/vendor ext4 rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc 0 0
/dev/block/mmcblk0p47 /cache f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
/dev/block/mmcblk0p48 /blackbox f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
tmpfs /storage tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755,gid=1000 0 0
/dev/block/dm-50 /data f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
tmpfs /linkerconfig tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
/dev/block/loop5 /apex/com.android.btservices@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop4 /apex/com.android.media.swcodec@351504000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop6 /apex/com.android.adbd@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop7 /apex/com.android.wifi@351610000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop4 /apex/com.android.media.swcodec ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop5 /apex/com.android.btservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop6 /apex/com.android.adbd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop7 /apex/com.android.wifi ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop8 /apex/com.android.nfcservices@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop9 /apex/com.android.virt@2 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop8 /apex/com.android.nfcservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop10 /apex/com.android.conscrypt@351412000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop11 /apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop9 /apex/com.android.virt ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop11 /apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop10 /apex/com.android.conscrypt ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop12 /apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop12 /apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop13 /apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop13 /apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop14 /apex/com.android.cellbroadcast@351511000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop15 /apex/com.android.neuralnetworks@351010040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop14 /apex/com.android.cellbroadcast ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop15 /apex/com.android.neuralnetworks ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop16 /apex/com.android.appsearch@351412000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop17 /apex/com.android.profiling@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop16 /apex/com.android.appsearch ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop17 /apex/com.android.profiling ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop19 /apex/com.android.configinfrastructure@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop19 /apex/com.android.configinfrastructure ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop18 /apex/com.android.apex.cts.shim@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop20 /apex/com.android.devicelock@342410000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop21 /apex/com.android.adservices@351537040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop20 /apex/com.android.devicelock ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop18 /apex/com.android.apex.cts.shim ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop22 /apex/com.android.healthfitness@351511060 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop21 /apex/com.android.adservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop22 /apex/com.android.healthfitness ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop23 /apex/com.android.ipsec@351410000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop24 /apex/com.android.rkpd@351310000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop26 /apex/com.android.os.statsd@351610000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop25 /apex/com.android.media@351504000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop23 /apex/com.android.ipsec ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop24 /apex/com.android.rkpd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop25 /apex/com.android.media ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop26 /apex/com.android.os.statsd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop28 /apex/com.android.extservices@351538083 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop29 /apex/com.android.uwb@351310040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop27 /apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop27 /apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop30 /apex/com.android.mediaprovider@351613160 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop29 /apex/com.android.uwb ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop28 /apex/com.android.extservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop30 /apex/com.android.mediaprovider ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop32 /apex/com.android.permission@351610020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop31 /apex/com.android.art@351610080 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop34 /apex/com.android.sdkext@351415000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop33 /apex/com.android.compos@2 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop32 /apex/com.android.permission ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop31 /apex/com.android.art ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop34 /apex/com.android.sdkext ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop33 /apex/com.android.compos ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop35 /apex/com.google.mainline.primary.libs@351165000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop37 /apex/com.android.ondevicepersonalization@351541000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop36 /apex/com.android.resolv@351510000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop38 /apex/com.android.tethering@351510080 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop37 /apex/com.android.ondevicepersonalization ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop36 /apex/com.android.resolv ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop38 /apex/com.android.tethering ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop39 /apex/com.android.scheduling@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop39 /apex/com.android.scheduling ext4 ro,dirsync,seclabel,nodev,noatime 0 0
tmpfs /apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=755 0 0
tmpfs /data_mirror tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,size=1907128k,nr_inodes=476782,mode=700,gid=1000 0 0
/dev/block/dm-50 /data_mirror/data_ce/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/data_de/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/misc_ce/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/misc_de/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/storage_area f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/cur_profiles f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/ref_profiles f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/data_ce/null/0 f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data/user/0 f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-10 /product/priv-app/Payjoy erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/fuse /mnt/installer/0/emulated fuse rw,lazytime,nosuid,nodev,noexec,noatime,user_id=0,group_id=0,allow_other 0 0
/dev/fuse /mnt/androidwritable/0/emulated fuse rw,lazytime,nosuid,nodev,noexec,noatime,user_id=0,group_id=0,allow_other 0 0
/dev/fuse /mnt/user/0/emulated fuse rw,lazytime,nosuid,nodev,noexec,noatime,user_id=0,group_id=0,allow_other 0 0
/dev/fuse /storage/emulated fuse rw,lazytime,nosuid,nodev,noexec,noatime,user_id=0,group_id=0,allow_other 0 0
/dev/block/dm-50 /mnt/pass_through/0/emulated f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0


cat /proc/diskstats
Fri Aug  1 12:01:53 CST 2025
   1       0 ram0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       1 ram1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       2 ram2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       3 ram3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       4 ram4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       5 ram5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       6 ram6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       7 ram7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       8 ram8 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       9 ram9 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      10 ram10 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      11 ram11 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      12 ram12 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      13 ram13 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      14 ram14 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      15 ram15 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   7       0 loop0 117 0 11416 154 0 0 0 0 0 164 154 0 0 0 0 0 0
   7       8 loop1 81 0 9704 80 0 0 0 0 0 152 80 0 0 0 0 0 0
   7      16 loop2 40 0 3440 48 0 0 0 0 0 64 48 0 0 0 0 0 0
   7      24 loop3 17 0 536 14 0 0 0 0 0 36 14 0 0 0 0 0 0
   7      32 loop4 418 0 38304 965 0 0 0 0 0 1192 965 0 0 0 0 0 0
   7      40 loop5 248 0 37368 532 0 0 0 0 0 840 532 0 0 0 0 0 0
   7      48 loop6 106 0 11544 144 0 0 0 0 0 216 144 0 0 0 0 0 0
   7      56 loop7 129 0 16256 705 0 0 0 0 0 268 705 0 0 0 0 0 0
   7      64 loop8 15 0 544 8 0 0 0 0 0 44 8 0 0 0 0 0 0
   7      72 loop9 24 0 464 10 0 0 0 0 0 64 10 0 0 0 0 0 0
   7      80 loop10 108 0 11528 292 0 0 0 0 0 392 292 0 0 0 0 0 0
   7      88 loop11 32 0 1544 13 0 0 0 0 0 88 13 0 0 0 0 0 0
   7      96 loop12 612 0 42488 711 0 0 0 0 0 1064 711 0 0 0 0 0 0
   7     104 loop13 88 0 9912 73 0 0 0 0 0 140 73 0 0 0 0 0 0
   7     112 loop14 150 0 28872 274 0 0 0 0 0 312 274 0 0 0 0 0 0
   7     120 loop15 5 0 40 0 0 0 0 0 0 8 0 0 0 0 0 0 0
 259       0 pmem0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       0 mmcblk0 64309 7351 3677924 77327 108741 1166 1015400 47171 0 156764 126600 280 15 1120264 1329 536 772
 179       1 mmcblk0p1 50 7 2156 139 44 12 200 149 0 480 289 0 0 0 0 0 0
 179       2 mmcblk0p2 66 0 528 214 61 0 480 57 0 520 272 0 0 0 0 0 0
 179       3 mmcblk0p3 5 0 136 8 0 0 0 0 0 28 8 0 0 0 0 0 0
 179       4 mmcblk0p4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       5 mmcblk0p5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       6 mmcblk0p6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       7 mmcblk0p7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       1 mmcblk0p8 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       2 mmcblk0p9 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       3 mmcblk0p10 94 2 32768 271 0 0 0 0 0 248 271 0 0 0 0 0 0
 259       4 mmcblk0p11 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       5 mmcblk0p12 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       6 mmcblk0p13 21 1 9704 109 3 0 16 1 0 88 110 0 0 0 0 0 0
 259       7 mmcblk0p14 15 0 6472 68 3 0 16 1 0 48 69 0 0 0 0 0 0
 259       8 mmcblk0p15 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       9 mmcblk0p16 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      10 mmcblk0p17 49 1 3680 81 35 15 344 19 0 112 100 0 0 0 0 0 0
 259      11 mmcblk0p18 105 1 30368 198 35 15 344 13 0 184 212 0 0 0 0 0 0
 259      12 mmcblk0p19 613 12 127264 883 12 0 48 4 0 1136 887 0 0 0 0 0 0
 259      13 mmcblk0p20 50 0 41432 758 0 0 0 0 0 448 758 0 0 0 0 0 0
 259      14 mmcblk0p21 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      15 mmcblk0p22 2 0 1536 20 0 0 0 0 0 24 20 0 0 0 0 0 0
 259      16 mmcblk0p23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      17 mmcblk0p24 13 0 9752 100 0 0 0 0 0 76 100 0 0 0 0 0 0
 259      18 mmcblk0p25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      19 mmcblk0p26 26 0 23576 134 0 0 0 0 0 100 134 0 0 0 0 0 0
 259      20 mmcblk0p27 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      21 mmcblk0p28 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      22 mmcblk0p29 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      23 mmcblk0p30 5 0 2072 17 0 0 0 0 0 32 17 0 0 0 0 0 0
 259      24 mmcblk0p31 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      25 mmcblk0p32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      26 mmcblk0p33 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      27 mmcblk0p34 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      28 mmcblk0p35 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      29 mmcblk0p36 2 0 16 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      30 mmcblk0p37 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      31 mmcblk0p38 2 0 16 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      32 mmcblk0p39 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      33 mmcblk0p40 2 0 16 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      34 mmcblk0p41 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      35 mmcblk0p42 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      36 mmcblk0p43 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      37 mmcblk0p44 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      38 mmcblk0p45 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      39 mmcblk0p46 36907 2909 2866472 47137 0 0 0 0 0 37060 47137 0 0 0 0 0 0
 259      40 mmcblk0p47 43 9 592 20 3 0 32 3 0 84 34 1 0 8 10 0 0
 259      41 mmcblk0p48 130 359 13912 353 71 7 19856 373 0 696 778 17 1 18296 51 0 0
 259      42 mmcblk0p49 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      43 mmcblk0p50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      44 mmcblk0p51 52 10 600 34 21 1 256 32 0 120 86 8 0 192 19 0 0
 259      45 mmcblk0p52 1 0 32 31 0 0 0 0 0 36 31 0 0 0 0 0 0
 259      46 mmcblk0p53 1 0 256 4 0 0 0 0 0 12 4 0 0 0 0 0 0
 259      47 mmcblk0p54 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      48 mmcblk0p55 1 0 256 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      49 mmcblk0p56 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      50 mmcblk0p57 1 0 256 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      51 mmcblk0p58 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      52 mmcblk0p59 1 0 256 4 0 0 0 0 0 8 4 0 0 0 0 0 0
 259      53 mmcblk0p60 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      54 mmcblk0p61 1 0 256 4 0 0 0 0 0 8 4 0 0 0 0 0 0
 259      55 mmcblk0p62 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      56 mmcblk0p63 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      57 mmcblk0p64 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      58 mmcblk0p65 29 0 5056 122 0 0 0 0 0 124 122 0 0 0 0 0 0
 259      59 mmcblk0p66 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      60 mmcblk0p67 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      61 mmcblk0p68 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      62 mmcblk0p69 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      63 mmcblk0p70 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      64 mmcblk0p71 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      65 mmcblk0p72 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      66 mmcblk0p73 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      67 mmcblk0p74 26007 4040 498120 26595 108453 1116 993808 46513 0 134808 74357 254 14 1101768 1248 0 0
 179       8 mmcblk0boot0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179      16 mmcblk0boot1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254       0 dm-0 15604 0 943384 28088 0 0 0 0 0 16172 28088 0 0 0 0 0 0
 254       1 dm-1 3991 0 257944 12420 0 0 0 0 0 6708 12420 0 0 0 0 0 0
 254       2 dm-2 3301 0 193984 7428 0 0 0 0 0 3984 7428 0 0 0 0 0 0
 254       3 dm-3 16319 0 1439408 14880 0 0 0 0 0 21684 14880 0 0 0 0 0 0
 254       4 dm-4 401 0 13112 540 0 0 0 0 0 740 540 0 0 0 0 0 0
 254       5 dm-5 193 0 18560 116 0 0 0 0 0 564 116 0 0 0 0 0 0
 254       6 dm-6 5 0 40 0 0 0 0 0 0 12 0 0 0 0 0 0 0
 254       7 dm-7 13793 0 928896 34276 0 0 0 0 0 16572 34276 0 0 0 0 0 0
 254       8 dm-8 3469 0 253768 13892 0 0 0 0 0 6956 13892 0 0 0 0 0 0
 254       9 dm-9 2809 0 190048 7084 0 0 0 0 0 4036 7084 0 0 0 0 0 0
 254      10 dm-10 269 0 12056 340 0 0 0 0 0 768 340 0 0 0 0 0 0
 254      11 dm-11 13785 0 1419136 23624 0 0 0 0 0 22580 23624 0 0 0 0 0 0
 254      12 dm-12 166 0 18344 152 0 0 0 0 0 584 152 0 0 0 0 0 0
 254      13 dm-13 4 0 32 0 0 0 0 0 0 12 0 0 0 0 0 0 0
   7     128 loop16 76 0 9344 124 0 0 0 0 0 208 124 0 0 0 0 0 0
   7     136 loop17 20 0 1112 22 0 0 0 0 0 52 22 0 0 0 0 0 0
   7     144 loop18 14 0 136 3 0 0 0 0 0 24 3 0 0 0 0 0 0
   7     152 loop19 22 0 1000 10 0 0 0 0 0 64 10 0 0 0 0 0 0
   7     160 loop20 90 0 11584 204 0 0 0 0 0 348 204 0 0 0 0 0 0
   7     168 loop21 137 0 18144 180 0 0 0 0 0 304 180 0 0 0 0 0 0
   7     176 loop22 113 0 16480 171 0 0 0 0 0 260 171 0 0 0 0 0 0
   7     184 loop23 24 0 1448 19 0 0 0 0 0 76 19 0 0 0 0 0 0
   7     192 loop24 32 0 2600 33 0 0 0 0 0 80 33 0 0 0 0 0 0
   7     200 loop25 101 0 9624 190 0 0 0 0 0 272 190 0 0 0 0 0 0
   7     208 loop26 51 0 3984 43 0 0 0 0 0 136 43 0 0 0 0 0 0
   7     216 loop27 498 0 42120 8673 0 0 0 0 0 1392 8673 0 0 0 0 0 0
   7     224 loop28 33 0 2864 55 0 0 0 0 0 116 55 0 0 0 0 0 0
   7     232 loop29 29 0 2568 38 0 0 0 0 0 68 38 0 0 0 0 0 0
   7     240 loop30 190 0 27208 511 0 0 0 0 0 708 511 0 0 0 0 0 0
   7     248 loop31 578 0 67264 1294 0 0 0 0 0 1428 1294 0 0 0 0 0 0
   7     256 loop32 385 0 52736 626 0 0 0 0 0 900 626 0 0 0 0 0 0
   7     264 loop33 17 0 160 4 0 0 0 0 0 36 4 0 0 0 0 0 0
   7     272 loop34 26 0 1416 14 0 0 0 0 0 72 14 0 0 0 0 0 0
   7     280 loop35 12 0 96 4 0 0 0 0 0 20 4 0 0 0 0 0 0
   7     288 loop36 45 0 7168 72 0 0 0 0 0 116 72 0 0 0 0 0 0
   7     296 loop37 47 0 4512 57 0 0 0 0 0 152 57 0 0 0 0 0 0
   7     304 loop38 188 0 16688 277 0 0 0 0 0 472 277 0 0 0 0 0 0
   7     312 loop39 15 0 208 2 0 0 0 0 0 36 2 0 0 0 0 0 0
   7     320 loop40 13022 0 104176 11468 101937 0 815496 65921 0 111476 77390 0 0 0 0 0 0
 254      50 dm-50 30044 0 497992 44768 109554 0 993824 73092 1 134944 119404 268 0 1101768 1544 0 0
 253       0 zram0 71140 0 569120 792 243542 0 1948336 6244 0 30896 7036 0 0 0 0 0 0


cat /proc/modules
Fri Aug  1 12:01:53 CST 2025
focaltech_touch_spi 192512 1 - Live 0x0000000000000000 (OE)
focaltech_side_fp 57344 0 - Live 0x0000000000000000 (OE)
kprobe_iomonitor 28672 0 - Live 0x0000000000000000 (E)
sla_core 24576 0 - Live 0x0000000000000000 (E)
unisoc_binder 28672 0 [permanent], Live 0x0000000000000000 (E)
unisoc_pnp 28672 0 - Live 0x0000000000000000 (E)
zram 65536 2 - Live 0x0000000000000000 (E)
zsmalloc 57344 1 zram, Live 0x0000000000000000 (E)
kts_sync 16384 0 - Live 0x0000000000000000 (E)
sprd_coresight_apetb_main 20480 0 - Live 0x0000000000000000 (E)
sprd_coresight_apetb_ctrl 16384 1 sprd_coresight_apetb_main, Live 0x0000000000000000 (E)
sprd_coresight_etm4x 131072 1 sprd_coresight_apetb_ctrl, Live 0x0000000000000000 (E)
sprd_coresight_replicator 24576 0 - Live 0x0000000000000000 (E)
sprd_coresight_funnel 24576 0 - Live 0x0000000000000000 (E)
sprd_coresight_tmc 40960 1 sprd_coresight_apetb_ctrl, Live 0x0000000000000000 (E)
sprd_coresight 98304 5 sprd_coresight_apetb_main,sprd_coresight_etm4x,sprd_coresight_replicator,sprd_coresight_funnel,sprd_coresight_tmc, Live 0x0000000000000000 (E)
ums9230_serdes 20480 0 - Live 0x0000000000000000 (E)
mipiserdes_base 20480 1 ums9230_serdes, Live 0x0000000000000000 (E)
unisoc_gnss_pmnotify_ctl 16384 1 - Live 0x0000000000000000 (OE)
unisoc_gnss_dbg 28672 2 - Live 0x0000000000000000 (OE)
unisoc_gnss_common_ctl_all 24576 1 unisoc_gnss_pmnotify_ctl, Live 0x0000000000000000 (OE)
sprd_fm 102400 0 - Live 0x0000000000000000 (OE)
sprdbt_tty 73728 2 - Live 0x0000000000000000 (OE)
sprd_compr_2stage_dma 53248 1 - Live 0x0000000000000000 (OE)
sprd_dmaengine_pcm 49152 1 - Live 0x0000000000000000 (OE)
snd_soc_sprd_vbc_fe 40960 1 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_vbc_v4 454656 2 snd_soc_sprd_vbc_fe,[permanent], Live 0x0000000000000000 (OE)
sprd_platform_pcm_routing 135168 1 - Live 0x0000000000000000 (OE)
snd_soc_sprd_pdm_r2p0 32768 0 - Live 0x0000000000000000 (OE)
snd_soc_sprd_dummy_codec 16384 0 - Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730_power_dev 28672 0 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730_power 32768 30 - Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730 229376 1 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_card 94208 7 focaltech_touch_spi,sprd_compr_2stage_dma,sprd_dmaengine_pcm,snd_soc_sprd_vbc_v4,snd_soc_sprd_pdm_r2p0,snd_soc_sprd_codec_sc2730_power,snd_soc_sprd_codec_sc2730,[permanent], Live 0x0000000000000000 (OE)
sprd_audcp_boot 16384 0 - Live 0x0000000000000000 (OE)
sprd_audcp_dvfs 40960 0 - Live 0x0000000000000000 (OE)
mcdt_hw_r2p0 36864 3 sprd_compr_2stage_dma,snd_soc_sprd_vbc_fe,snd_soc_sprd_vbc_v4, Live 0x0000000000000000 (OE)
audio_pipe 40960 2 - Live 0x0000000000000000 (OE)
sprd_apipe 32768 3 - Live 0x0000000000000000 (OE)
sprd_audio_usb_offload 20480 0 - Live 0x0000000000000000 (OE)
audio_dsp_dump 57344 8 - Live 0x0000000000000000 (OE)
audio_sipc 53248 7 sprd_compr_2stage_dma,snd_soc_sprd_vbc_v4,snd_soc_sprd_pdm_r2p0,sprd_audcp_boot,sprd_audcp_dvfs,audio_pipe,audio_dsp_dump, Live 0x0000000000000000 (OE)
audio_mem 36864 5 sprd_compr_2stage_dma,sprd_dmaengine_pcm,sprd_audcp_boot,audio_dsp_dump,audio_sipc, Live 0x0000000000000000 (OE)
agdsp_pd 32768 2 audio_dsp_dump,audio_sipc, Live 0x0000000000000000 (OE)
sprd_cp_dvfs 32768 0 - Live 0x0000000000000000 (E)
musb_sprd 61440 0 - Live 0x0000000000000000 (E)
musb_hdrc 286720 2 sprd_audio_usb_offload,musb_sprd, Live 0x0000000000000000 (E)
sprd_pmic_wdt 20480 0 - Live 0x0000000000000000 (E)
ims_bridge 131072 0 - Live 0x0000000000000000 (E)
sprd_map 20480 0 - Live 0x0000000000000000 (E)
bq2597x_charger 40960 0 - Live 0x0000000000000000 (E)
sc27xx_fast_charger 20480 0 - Live 0x0000000000000000 (E)
sc27xx_pd 53248 0 - Live 0x0000000000000000 (E)
sprd_typec_displayport 20480 0 - Live 0x0000000000000000 (E)
sy65153_wireless_charger 36864 0 - Live 0x0000000000000000 (E)
sgm41516_charger 40960 0 - Live 0x0000000000000000 (E)
bq2560x_charger 49152 2 - Live 0x0000000000000000 (E)
sprd_uid 16384 0 - Live 0x0000000000000000 (E)
sc27xx_fuel_gauge 118784 0 - Live 0x0000000000000000 (E)
sprd_charger_manager 176128 3 bq2597x_charger,sy65153_wireless_charger,sc27xx_fuel_gauge, Live 0x0000000000000000 (E)
sprd_battery_info 20480 4 sgm41516_charger,bq2560x_charger,sc27xx_fuel_gauge,sprd_charger_manager, Live 0x0000000000000000 (E)
sc27xx_typec 40960 1 musb_sprd, Live 0x0000000000000000 (E)
phy_sprd_qogirl6 28672 3 - Live 0x0000000000000000 (E)
phy_sprd_commonphy 16384 2 musb_sprd,phy_sprd_qogirl6, Live 0x0000000000000000 (E)
sprd_tcpm 106496 3 sc27xx_pd,sprd_charger_manager,sc27xx_typec, Live 0x0000000000000000 (E)
sc27xx_vibra 16384 0 - Live 0x0000000000000000 (E)
sprd_virt_thm 16384 0 - Live 0x0000000000000000 (E)
thermal_generic_adc 16384 0 - Live 0x0000000000000000 (E)
sprd_wlan_combo 712704 0 - Live 0x0000000000000000 (OE)
unisoc_wcn_bsp 643072 5 unisoc_gnss_common_ctl_all,sprd_fm,sprdbt_tty,sprd_wlan_combo, Live 0x0000000000000000 (OE)
sprd_bcl 20480 0 - Live 0x0000000000000000 (E)
sprd_freq_limit 20480 0 - Live 0x0000000000000000 (E)
cpufreq_userspace 20480 0 - Live 0x0000000000000000 (E)
dmc_drv 28672 0 - Live 0x0000000000000000 (E)
sprd_ddr_dvfs 69632 0 - Live 0x0000000000000000 (E)
sc27xx_tsensor_thermal 16384 0 - Live 0x0000000000000000 (E)
sc27xx_poweroff 16384 0 - Live 0x0000000000000000 (E)
sc27xx_adc 53248 0 - Live 0x0000000000000000 (E)
pwm_sprd 16384 1 - Live 0x0000000000000000 (E)
spi_sprd 32768 0 - Live 0x0000000000000000 (E)
pinctrl_sprd_qogirl6 40960 0 - Live 0x0000000000000000 (E)
pinctrl_sprd 28672 1 pinctrl_sprd_qogirl6, Live 0x0000000000000000 (E)
ledtrig_pattern 16384 0 - Live 0x0000000000000000 (E)
leds_sc27xx_bltc 16384 0 - Live 0x0000000000000000 (E)
jpg 49152 0 - Live 0x0000000000000000 (OE)
vpu 77824 0 - Live 0x0000000000000000 (OE)
sprd_vsp_pw_domain 20480 0 - Live 0x0000000000000000 (OE)
mali_kbase 1486848 32 - Live 0x0000000000000000 (OE)
pinctrl 20480 0 - Live 0x0000000000000000 (E)
gpio 20480 0 - Live 0x0000000000000000 (E)
core 16384 2 pinctrl,gpio, Live 0x0000000000000000 (E)
extcon_usb_gpio 16384 0 - Live 0x0000000000000000 (E)
sprd_cpu_cooling 45056 0 - Live 0x0000000000000000 (E)
sprd_drm 741376 4 focaltech_touch_spi, Live 0x0000000000000000 (OE)
unisoc_mm_reclaim 36864 1 sprd_drm, Live 0x0000000000000000 (E)
sprd_gsp 311296 1 sprd_drm, Live 0x0000000000000000 (OE)
unisoc_multi_control 16384 0 - Live 0x0000000000000000 (E)
mmdvfs 196608 0 - Live 0x0000000000000000 (OE)
apsys_dvfs 184320 2 vpu,sprd_drm, Live 0x0000000000000000 (E)
sprd_cpp 110592 0 - Live 0x0000000000000000 (OE)
sprd_camera 2011136 0 - Live 0x0000000000000000 (OE)
sprd_sensor 98304 1 sprd_camera, Live 0x0000000000000000 (OE)
sprd_dmabuf 36864 2 sprd_cpp,sprd_camera, Live 0x0000000000000000 (OE)
cma_heap 28672 0 [permanent], Live 0x0000000000000000 (E)
ion_ipc_trusty 20480 1 cma_heap, Live 0x0000000000000000 (E)
system_heap 28672 93 [permanent], Live 0x0000000000000000 (E)
unisoc_iommu 106496 7 jpg,vpu,sprd_drm,sprd_gsp,sprd_cpp,sprd_camera,system_heap, Live 0x0000000000000000 (E)
sprd_dma 32768 2 - Live 0x0000000000000000 (E)
unisoc_mm_emem 24576 3 zram,mali_kbase,system_heap, Live 0x0000000000000000 (E)
sprd_camsys_pw_domain 69632 5 jpg,mmdvfs,sprd_cpp,sprd_camera,sprd_sensor, Live 0x0000000000000000 (OE)
flash_ic_aw36515 28672 0 - Live 0x0000000000000000 (OE)
flash_ic_ocp81375 24576 0 - Live 0x0000000000000000 (OE)
flash_ic_ocp8137 20480 0 - Live 0x0000000000000000 (OE)
sprd_flash_drv 24576 4 sprd_camera,flash_ic_aw36515,flash_ic_ocp81375,flash_ic_ocp8137, Live 0x0000000000000000 (OE)
sprd_aphang 53248 0 - Live 0x0000000000000000 (E)
unisoc_last_kmsg 57344 1 sprd_aphang, Live 0x0000000000000000 (E)
sprd_usbpinmux 20480 2 musb_sprd,phy_sprd_qogirl6, Live 0x0000000000000000 (E)
sprd_bc1p2 28672 1 phy_sprd_qogirl6, Live 0x0000000000000000 (E)
cfg80211 1470464 1 sprd_wlan_combo, Live 0x0000000000000000 (E)
sprd_shm 28672 0 - Live 0x0000000000000000 (E)
trusty_virtio 28672 0 - Live 0x0000000000000000 (E)
trusty_ipc 53248 24 unisoc_wcn_bsp,sprd_drm,sprd_camera,ion_ipc_trusty, Live 0x0000000000000000 (E)
trusty_irq 28672 0 - Live 0x0000000000000000 (E)
trusty_log 28672 1 - Live 0x0000000000000000 (E)
trusty_pm 16384 0 - Live 0x0000000000000000 (E)
trusty 40960 8 mali_kbase,sprd_drm,sprd_gsp,sprd_shm,trusty_virtio,trusty_irq,trusty_log,trusty_pm, Live 0x0000000000000000 (E)
unisoc_userlog 20480 4 - Live 0x0000000000000000 (E)
tms_device_modules 57344 1 - Live 0x0000000000000000 (E)
sprd_trng 16384 0 - Live 0x0000000000000000 (E)
sprd_cache_print 20480 0 - Live 0x0000000000000000 (E)
sprd_usb_f_rndis 61440 0 - Live 0x0000000000000000 (E)
sprd_u_ether 45056 1 sprd_usb_f_rndis, Live 0x0000000000000000 (E)
sprd_usb_f_serial 24576 16 - Live 0x0000000000000000 (E)
sprd_u_serial 36864 5 sprd_usb_f_serial, Live 0x0000000000000000 (E)
sblock_bridge 20480 0 - Live 0x0000000000000000 (E)
sbuf_bridge 20480 0 - Live 0x0000000000000000 (E)
slog_bridge 24576 0 - Live 0x0000000000000000 (E)
sprd_iq 28672 0 - Live 0x0000000000000000 (E)
usb_f_vser 53248 4 slog_bridge,sprd_iq, Live 0x0000000000000000 (E)
sprd_cp_dump 28672 0 - Live 0x0000000000000000 (E)
sprd_modem_loader 36864 0 - Live 0x0000000000000000 (E)
seth 36864 0 - Live 0x0000000000000000 (E)
sipx 40960 1 seth, Live 0x0000000000000000 (E)
spool 28672 1 - Live 0x0000000000000000 (E)
spipe 20480 18 - Live 0x0000000000000000 (E)
sprd_sipc_virt_bus 16384 0 - Live 0x0000000000000000 (E)
sensorhub 53248 3 - Live 0x0000000000000000 (E)
sr_hwinfo 20480 5 focaltech_touch_spi,focaltech_side_fp,sprd_drm,tms_device_modules,sensorhub,[permanent], Live 0x0000000000000000 (E)
sipc_core 196608 19 sprd_fm,sprdbt_tty,agdsp_pd,sprd_cp_dvfs,sprd_pmic_wdt,ims_bridge,sprd_wlan_combo,unisoc_wcn_bsp,sprd_ddr_dvfs,sblock_bridge,sbuf_bridge,slog_bridge,sprd_iq,sprd_modem_loader,seth,sipx,spool,spipe,sensorhub, Live 0x0000000000000000 (E)
sprd_pdbg 53248 2 unisoc_wcn_bsp,sipc_core, Live 0x0000000000000000 (E)
unisoc_mailbox 36864 7 sipc_core,[permanent], Live 0x0000000000000000 (E)
sprd_power_manager 32768 3 sbuf_bridge,sprd_modem_loader,sipc_core, Live 0x0000000000000000 (E)
sprd_pmic_syscon 16384 0 - Live 0x0000000000000000 (E)
shutdown_detect 20480 0 [permanent], Live 0x0000000000000000 (E)
sdhci_sprd 188416 0 - Live 0x0000000000000000 (E)
mmc_hsq 16384 1 sdhci_sprd, Live 0x0000000000000000 (E)
mmc_swcq 73728 1 sdhci_sprd, Live 0x0000000000000000 (E)
gpio_pmic_eic_sprd 16384 10 - Live 0x0000000000000000 (E)
gpio_sprd 16384 21 - Live 0x0000000000000000 (E)
gpio_eic_sprd 20480 4 - Live 0x0000000000000000 (E)
sprd_cpufreq_v2 36864 0 [permanent], Live 0x0000000000000000 (E)
sprd_thermal_ctl 28672 1 mali_kbase, Live 0x0000000000000000 (E)
sprd_soc_thm 16384 0 - Live 0x0000000000000000 (E)
sprd_thermal 20480 0 - Live 0x0000000000000000 (E)
trusty_tui 24576 0 - Live 0x0000000000000000 (E)
sprd_7sreset 16384 0 - Live 0x0000000000000000 (E)
i2c_sprd_hw_v2 24576 0 - Live 0x0000000000000000 (E)
i2c_sprd 32768 0 - Live 0x0000000000000000 (E)
ufs_sprd 221184 0 - Live 0x0000000000000000 (E)
rpmb 36864 1 ufs_sprd, Live 0x0000000000000000 (E)
nvmem_sprd_efuse 16384 0 - Live 0x0000000000000000 (E)
nvmem_sprd_cache_efuse 16384 0 - Live 0x0000000000000000 (E)
nvmem_sc27xx_efuse 16384 0 - Live 0x0000000000000000 (E)
sprd_hwspinlock 16384 3 - Live 0x0000000000000000 (E)
sprd_soc_id 20480 3 mmdvfs,apsys_dvfs,ufs_sprd, Live 0x0000000000000000 (E)
sc2730_regulator 24576 7 - Live 0x0000000000000000 (E)
rtc_sc27xx 20480 1 - Live 0x0000000000000000 (E)
sprd_pmic_spi 20480 0 - Live 0x0000000000000000 (E)
spi_sprd_adi 32768 0 - Live 0x0000000000000000 (E)
unisoc_sched 245760 4 sprd_cpu_cooling,unisoc_mm_reclaim,[permanent], Live 0x0000000000000000 (E)
iolimit 28672 0 [permanent], Live 0x0000000000000000 (E)
ums9230_clk 159744 141 - Live 0x0000000000000000 (E)
clk_sprd 32768 1 ums9230_clk, Live 0x0000000000000000 (E)
sprd_time_sync_cp 16384 0 [permanent], Live 0x0000000000000000 (E)
sprd_time_sync 20480 2 sprd_time_sync_cp, Live 0x0000000000000000 (E)
sprd_systimer 16384 3 sensorhub,sprd_pdbg,sprd_time_sync,[permanent], Live 0x0000000000000000 (E)
sprd_sip_svc 24576 5 sprd_pmic_wdt,sprd_aphang,sprd_pdbg,sprd_cpufreq_v2,ufs_sprd,[permanent], Live 0x0000000000000000 (E)
sprd_wdt_fiq 28672 1 sprd_aphang, Live 0x0000000000000000 (E)
regmap_hook 16384 0 - Live 0x0000000000000000 (E)
timer_sprd 16384 0 [permanent], Live 0x0000000000000000 (E)
native_hang_monitor 36864 1 - Live 0x0000000000000000 (E)
sysdump 114688 14 dmc_drv,sprd_ddr_dvfs,sprd_aphang,unisoc_last_kmsg,sprd_cp_dump,sipc_core,shutdown_detect,ufs_sprd,sprd_pmic_spi,spi_sprd_adi,sprd_wdt_fiq,native_hang_monitor,[permanent], Live 0x0000000000000000 (E)
printk_cpuid 16384 0 - Live 0x0000000000000000 (E)


cat /proc/cpuinfo
Fri Aug  1 12:01:53 CST 2025
processor	: 0
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 1
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 2
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 3
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 4
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 5
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 6
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x3
CPU part	: 0xd0a
CPU revision	: 1

processor	: 7
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x3
CPU part	: 0xd0a
CPU revision	: 1



getprop
Fri Aug  1 12:01:54 CST 2025
[aaudio.mmap_exclusive_policy]: [2]
[aaudio.mmap_policy]: [2]
[af.media.systemready.state]: [true]
[apex.all.ready]: [true]
[apr.flag.bootmode]: [1]
[bluetooth.device.class_of_device]: [90,2,12]
[bluetooth.device.default_name]: [SPRD_V_Common]
[bluetooth.profile.a2dp.source.enabled]: [true]
[bluetooth.profile.asha.central.enabled]: [true]
[bluetooth.profile.avrcp.target.enabled]: [true]
[bluetooth.profile.bas.client.enabled]: [true]
[bluetooth.profile.gatt.enabled]: [true]
[bluetooth.profile.hfp.ag.enabled]: [true]
[bluetooth.profile.hid.device.enabled]: [true]
[bluetooth.profile.hid.host.enabled]: [true]
[bluetooth.profile.map.server.enabled]: [true]
[bluetooth.profile.opp.enabled]: [true]
[bluetooth.profile.pan.nap.enabled]: [true]
[bluetooth.profile.pan.panu.enabled]: [true]
[bluetooth.profile.pbap.server.enabled]: [true]
[bootreceiver.enable]: [1]
[build.version.extensions.ad_services]: [16]
[build.version.extensions.r]: [16]
[build.version.extensions.s]: [16]
[build.version.extensions.t]: [16]
[build.version.extensions.u]: [16]
[build.version.extensions.v]: [16]
[cache_key.bluetooth.bluetooth_adapter_get_connection_state]: [-5341421479226049026]
[cache_key.bluetooth.bluetooth_adapter_get_profile_connection_state]: [-5341421479226049028]
[cache_key.bluetooth.bluetooth_adapter_get_state]: [-5341421479226049021]
[cache_key.bluetooth.bluetooth_adapter_is_offloaded_filtering_supported]: [-5341421479226049022]
[cache_key.bluetooth.bluetooth_device_get_bond_state]: [-5341421479226049025]
[cache_key.bluetooth.bluetooth_map_get_connection_state]: [-5341421479226049024]
[cache_key.bluetooth.bluetooth_sap_get_connection_state]: [-53414214***********]
[cache_key.display_info]: [5531053580119751132]
[cache_key.get_packages_for_uid]: [5531053580119751139]
[cache_key.has_system_feature]: [5531053580119750831]
[cache_key.is_compat_change_enabled]: [5531053580119751128]
[cache_key.is_interactive]: [5531053580119751118]
[cache_key.is_power_save_mode]: [5531053580119750849]
[cache_key.is_user_unlocked]: [5531053580119750887]
[cache_key.location_enabled]: [5531053580119750847]
[cache_key.package_info]: [5531053580119751129]
[cache_key.system_server.accounts_data]: [5531053580119750909]
[cache_key.system_server.connectionless_stylus_handwriting]: [5531053580119751049]
[cache_key.system_server.device_policy_manager_caches]: [5531053580119750844]
[cache_key.system_server.get_credential_type]: [5531053580119750848]
[cache_key.system_server.get_night_mode]: [5531053580119750842]
[cache_key.system_server.stylus_handwriting]: [5531053580119751048]
[cache_key.telephony.phone_account_to_subid]: [-2731624739084973293]
[cache_key.telephony.subscription_manager_service]: [-2731624739084973298]
[camera.disable_zsl_mode]: [1]
[dalvik.vm.appimageformat]: [lz4]
[dalvik.vm.dex2oat-Xms]: [64m]
[dalvik.vm.dex2oat-Xmx]: [512m]
[dalvik.vm.dex2oat-cpu-set]: [0,1,2,3]
[dalvik.vm.dex2oat-max-image-block-size]: [524288]
[dalvik.vm.dex2oat-minidebuginfo]: [true]
[dalvik.vm.dex2oat-resolve-startup-strings]: [true]
[dalvik.vm.dex2oat-threads]: [4]
[dalvik.vm.dex2oat64.enabled]: [true]
[dalvik.vm.dexopt.secondary]: [true]
[dalvik.vm.dexopt.thermal-cutoff]: [2]
[dalvik.vm.enable_pr_dexopt]: [true]
[dalvik.vm.finalizer-timeout-ms]: [30000]
[dalvik.vm.heapgrowthlimit]: [256m]
[dalvik.vm.heapmaxfree]: [16m]
[dalvik.vm.heapminfree]: [2m]
[dalvik.vm.heapsize]: [512m]
[dalvik.vm.heapstartsize]: [8m]
[dalvik.vm.heaptargetutilization]: [0.6]
[dalvik.vm.image-dex2oat-Xms]: [64m]
[dalvik.vm.image-dex2oat-Xmx]: [64m]
[dalvik.vm.isa.arm.features]: [default]
[dalvik.vm.isa.arm.variant]: [cortex-a55]
[dalvik.vm.isa.arm64.features]: [default]
[dalvik.vm.isa.arm64.variant]: [cortex-a75]
[dalvik.vm.madvise.artfile.size]: [4294967295]
[dalvik.vm.madvise.odexfile.size]: [104857600]
[dalvik.vm.madvise.vdexfile.size]: [104857600]
[dalvik.vm.minidebuginfo]: [true]
[dalvik.vm.thread-suspend-timeout-ms]: [40000]
[dalvik.vm.usap_pool_enabled]: [true]
[dalvik.vm.usap_pool_refill_delay_ms]: [3000]
[dalvik.vm.usap_pool_size_max]: [4]
[dalvik.vm.usap_pool_size_min]: [1]
[dalvik.vm.usap_refill_threshold]: [1]
[dalvik.vm.useartservice]: [true]
[dalvik.vm.usejit]: [true]
[debug.atrace.tags.enableflags]: [0]
[debug.force_rtl]: [false]
[debug.fwk.enable_adpf_cpu_hint]: [false]
[debug.hwui.skia_tracing_enabled]: [false]
[debug.hwui.skia_use_perfetto_track_events]: [false]
[debug.hwui.use_hint_manager]: [true]
[debug.perfetto.sdk_sysprop_guard_generation]: [0]
[debug.renderengine.backend]: [skiaglthreaded]
[debug.renderengine.skia_tracing_enabled]: [false]
[debug.renderengine.skia_use_perfetto_track_events]: [false]
[debug.sf.auto_latch_unsignaled]: [false]
[debug.sf.enable_adpf_cpu_hint]: [true]
[debug.sf.enable_changezorder_flag]: [1]
[debug.sf.enable_gl_backpressure]: [false]
[debug.sf.enable_gpu_security]: [1]
[debug.sf.enable_skip_rotation]: [1]
[debug.sf.high_fps_late_app_phase_offset_ns]: [-4000000]
[debug.sf.high_fps_late_sf_phase_offset_ns]: [-3000000]
[debug.sf.hwc.min.duration]: [3000000]
[debug.sf.long_screencapture_counts]: [10]
[debug.sf.treat_170m_as_sRGB]: [1]
[debug.stagefright.c2inputsurface]: [-1]
[debug.tracing.battery_status]: [3]
[debug.tracing.device_state]: [0:DEFAULT]
[debug.tracing.plug_type]: [0]
[debug.tracing.screen_brightness]: [0.4015748]
[debug.tracing.screen_state]: [2]
[dev.bootcomplete]: [1]
[dev.mnt.blk.blackbox]: [mmcblk0p48]
[dev.mnt.blk.cache]: [mmcblk0p47]
[dev.mnt.blk.data]: [mmcblk0p74]
[dev.mnt.blk.data.user.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.cur_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.ref_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.storage_area]: [mmcblk0p74]
[dev.mnt.blk.metadata]: [mmcblk0p51]
[dev.mnt.blk.mnt.vendor]: [mmcblk0p1]
[dev.mnt.blk.odm]: [mmcblk0p46]
[dev.mnt.blk.product]: [mmcblk0p46]
[dev.mnt.blk.product.priv-app.Payjoy]: [mmcblk0p46]
[dev.mnt.blk.root]: [mmcblk0p46]
[dev.mnt.blk.system_dlkm]: [mmcblk0p46]
[dev.mnt.blk.system_ext]: [mmcblk0p46]
[dev.mnt.blk.vendor]: [mmcblk0p46]
[dev.mnt.blk.vendor_dlkm]: [mmcblk0p46]
[dev.mnt.dev.blackbox]: [mmcblk0p48]
[dev.mnt.dev.cache]: [mmcblk0p47]
[dev.mnt.dev.data]: [dm-50]
[dev.mnt.dev.data.user.0]: [dm-50]
[dev.mnt.dev.data_mirror.cur_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null.0]: [dm-50]
[dev.mnt.dev.data_mirror.data_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.ref_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.storage_area]: [dm-50]
[dev.mnt.dev.metadata]: [mmcblk0p51]
[dev.mnt.dev.mnt.vendor]: [mmcblk0p1]
[dev.mnt.dev.odm]: [dm-10]
[dev.mnt.dev.product]: [dm-11]
[dev.mnt.dev.product.priv-app.Payjoy]: [dm-10]
[dev.mnt.dev.root]: [dm-7]
[dev.mnt.dev.system_dlkm]: [dm-13]
[dev.mnt.dev.system_ext]: [dm-8]
[dev.mnt.dev.vendor]: [dm-9]
[dev.mnt.dev.vendor_dlkm]: [dm-12]
[dev.mnt.rootdisk.blackbox]: [mmcblk0]
[dev.mnt.rootdisk.cache]: [mmcblk0]
[dev.mnt.rootdisk.data]: [mmcblk0]
[dev.mnt.rootdisk.data.user.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.cur_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.ref_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.storage_area]: [mmcblk0]
[dev.mnt.rootdisk.metadata]: [mmcblk0]
[dev.mnt.rootdisk.mnt.vendor]: [mmcblk0]
[dev.mnt.rootdisk.odm]: [mmcblk0]
[dev.mnt.rootdisk.product]: [mmcblk0]
[dev.mnt.rootdisk.product.priv-app.Payjoy]: [mmcblk0]
[dev.mnt.rootdisk.root]: [mmcblk0]
[dev.mnt.rootdisk.system_dlkm]: [mmcblk0]
[dev.mnt.rootdisk.system_ext]: [mmcblk0]
[dev.mnt.rootdisk.vendor]: [mmcblk0]
[dev.mnt.rootdisk.vendor_dlkm]: [mmcblk0]
[dumpstate.last_id]: [1]
[graphics.gpu.profiler.support]: [true]
[gsm.client.base]: [android-tcl]
[gsm.current.phone-type]: [1,1]
[gsm.network.type]: [LTE,Unknown]
[gsm.operator.alpha]: [中国联通,]
[gsm.operator.iso-country]: [cn,]
[gsm.operator.isroaming]: [false,false]
[gsm.operator.numeric]: [46001,]
[gsm.sim.state]: [ABSENT,ABSENT]
[gsm.sys.sim.rtt.allowed]: [,0]
[gsm.sys.sim.volte.allowedplmn]: [,0]
[gsm.sys.volte.state]: [0,0]
[gsm.sys.vowifi.state]: [0,0]
[gsm.version.baseband]: [4G_MODEM_22B_W24.36.3|qogirl6_modem,4G_MODEM_22B_W24.36.3|qogirl6_modem]
[gsm.version.ril-impl]: [android reference-ril 1.0]
[init.svc.aconfigd]: [stopped]
[init.svc.aconfigd-mainline-init]: [stopped]
[init.svc.aconfigd-platform-init]: [stopped]
[init.svc.adbd]: [running]
[init.svc.apexd]: [stopped]
[init.svc.apexd-bootstrap]: [stopped]
[init.svc.apexd-snapshotde]: [stopped]
[init.svc.art_boot]: [stopped]
[init.svc.audio_parameter_parser_service]: [running]
[init.svc.audio_tunning_service]: [running]
[init.svc.audioserver]: [running]
[init.svc.bootanim]: [stopped]
[init.svc.boringssl_self_test32]: [stopped]
[init.svc.boringssl_self_test32_vendor]: [stopped]
[init.svc.boringssl_self_test64]: [stopped]
[init.svc.boringssl_self_test64_vendor]: [stopped]
[init.svc.boringssl_self_test_apex32]: [stopped]
[init.svc.boringssl_self_test_apex64]: [stopped]
[init.svc.bpfloader]: [stopped]
[init.svc.cameraserver]: [running]
[init.svc.create_splloader_dual_slot_byname_path]: [stopped]
[init.svc.credstore]: [running]
[init.svc.deletecorefile]: [stopped]
[init.svc.derive_classpath]: [stopped]
[init.svc.derive_sdk]: [stopped]
[init.svc.dmesgd]: [stopped]
[init.svc.drm]: [running]
[init.svc.ext_data]: [running]
[init.svc.fwklog]: [stopped]
[init.svc.gatekeeperd]: [running]
[init.svc.gpsd]: [running]
[init.svc.gpu]: [running]
[init.svc.heapprofd]: [stopped]
[init.svc.hidl_memory]: [running]
[init.svc.hwservicemanager]: [running]
[init.svc.idmap2d]: [stopped]
[init.svc.ims_bridged]: [running]
[init.svc.incidentd]: [running]
[init.svc.insmod-sh]: [stopped]
[init.svc.installd]: [running]
[init.svc.keystore2]: [running]
[init.svc.linkturbonative]: [running]
[init.svc.lmkd]: [running]
[init.svc.logd]: [running]
[init.svc.logd-auditctl]: [stopped]
[init.svc.logd-reinit]: [stopped]
[init.svc.media]: [running]
[init.svc.media.swcodec]: [running]
[init.svc.media.unisoc.codec2]: [running]
[init.svc.mediadrm]: [running]
[init.svc.mediaextractor]: [running]
[init.svc.mediametrics]: [running]
[init.svc.minidumpd]: [stopped]
[init.svc.miscdata_hal_service]: [running]
[init.svc.misctrl]: [stopped]
[init.svc.netd]: [running]
[init.svc.nfc_hal_service.tms.aidl]: [running]
[init.svc.nhmonitor]: [stopped]
[init.svc.odsign]: [stopped]
[init.svc.perfetto_persistent_sysui_tracing_for_bugreport]: [stopped]
[init.svc.phasecheckserver]: [running]
[init.svc.poweronlog]: [stopped]
[init.svc.prng_seeder]: [running]
[init.svc.remotedisplay]: [running]
[init.svc.servicemanager]: [running]
[init.svc.slogmodem]: [running]
[init.svc.sprd_networkcontrol]: [stopped]
[init.svc.srmi_proxyd]: [running]
[init.svc.statsd]: [running]
[init.svc.storaged]: [running]
[init.svc.surfaceflinger]: [running]
[init.svc.swappiness-sh]: [stopped]
[init.svc.systemDebuggerd]: [stopped]
[init.svc.system_suspend]: [running]
[init.svc.tombstoned]: [running]
[init.svc.tool_service]: [running]
[init.svc.traced]: [running]
[init.svc.traced_perf]: [stopped]
[init.svc.traced_probes]: [running]
[init.svc.ueventd]: [running]
[init.svc.ufs_ffu]: [stopped]
[init.svc.uniber]: [running]
[init.svc.unionpnp_service]: [running]
[init.svc.uniresctlopt]: [running]
[init.svc.uniview]: [running]
[init.svc.update_engine]: [running]
[init.svc.update_verifier]: [stopped]
[init.svc.usbd]: [stopped]
[init.svc.vndservicemanager]: [running]
[init.svc.vold]: [running]
[init.svc.watchdogd]: [stopped]
[init.svc.wificond]: [running]
[init.svc.wpa_supplicant]: [running]
[init.svc.ylog]: [running]
[init.svc.yloglite]: [stopped]
[init.svc.zramwb-sh]: [stopped]
[init.svc.zygote]: [running]
[init.svc.zygote_secondary]: [running]
[log.tag.APM_AudioPolicyManager]: [D]
[log.tag.stats_log]: [I]
[logd.ready]: [true]
[net.bt.name]: [Android]
[nfc.fw.downloadmode_force]: [0]
[oem_trusted_authority]: [com.sprd.android.USCPhotosProvider.providers.SpecialTypesProvider]
[oem_trusted_certificate]: [E3AAF983CBE30BA04FC5A3AF4605F9A5315A8FE1]
[partition.odm.verified.check_at_most_once]: [0]
[partition.product.verified.check_at_most_once]: [0]
[partition.system.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified]: [2]
[partition.system_dlkm.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified.hash_alg]: [sha256]
[partition.system_dlkm.verified.root_digest]: [754aa385e82b109a204df5a9ac7e1dd310755029e5fb1e572290773a836bdb32]
[partition.system_ext.verified.check_at_most_once]: [0]
[partition.vendor.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified]: [2]
[partition.vendor_dlkm.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified.hash_alg]: [sha256]
[partition.vendor_dlkm.verified.root_digest]: [41afba0576aa24f316af0c0670a52ff8eca954509ead1b9c7e23464fbbd70656]
[persist.audio.bigvolume.enabled]: [false]
[persist.audio.bigvolume.switch]: [false]
[persist.device_config.aconfig_flags.codec_fwk.com.android.media.codec.flags.aidl_hal]: [true]
[persist.netmon.linger]: [20000]
[persist.nhmonitor.enable]: [on]
[persist.radio.multisim.config]: [dsds]
[persist.radio.psregstate]: [0,0]
[persist.storage.type]: [2]
[persist.sys.3d.calibraion]: [1]
[persist.sys.anti_aging.aging_state]: [1]
[persist.sys.apr.autoupload]: [1]
[persist.sys.apr.enabled]: [0]
[persist.sys.apr.exceptionnode]: [0]
[persist.sys.apr.intervaltime]: [1]
[persist.sys.apr.lifetime]: [0]
[persist.sys.apr.reload]: [0]
[persist.sys.apr.reportlevel]: [2]
[persist.sys.apr.rlchanged]: [800]
[persist.sys.apr.testgroup]: [CSSLAB]
[persist.sys.apr.timechanged]: [180]
[persist.sys.audio.source]: [true]
[persist.sys.bl.clearuserdata]: [true]
[persist.sys.cam3.multi.cam.id]: [2]
[persist.sys.cam3.type]: [back_blur]
[persist.sys.choreographer.activity_cold_start_insert_frame]: [true]
[persist.sys.choreographer.fling_insert_frame]: [true]
[persist.sys.choreographer.pre_animation_load]: [false]
[persist.sys.dalvik.vm.lib.2]: [libart.so]
[persist.sys.displayinset.top]: [0]
[persist.sys.engineer.enabled]: [false]
[persist.sys.extrainfo]: []
[persist.sys.firstboot]: [DONE]
[persist.sys.firstboot_complete]: [1]
[persist.sys.fuse]: [true]
[persist.sys.fuse.passthrough.enable]: [true]
[persist.sys.gms]: [1]
[persist.sys.heartbeat.enable]: [1]
[persist.sys.lmk.reportkills]: [true]
[persist.sys.locale]: [zh-Hans-CN]
[persist.sys.log.yloglite]: [0]
[persist.sys.navbar.overlay]: [false]
[persist.sys.power.touch]: [1]
[persist.sys.pq.cabc.enabled]: [1]
[persist.sys.pq.dci.enabled]: [1]
[persist.sys.pq.enabled]: [1]
[persist.sys.private_features.enable]: [1]
[persist.sys.pwctl.appidle]: [1]
[persist.sys.pwctl.appidle.force]: [1]
[persist.sys.pwctl.appstats]: [0]
[persist.sys.pwctl.bgclean]: [1]
[persist.sys.pwctl.enable]: [1]
[persist.sys.pwctl.gps]: [1]
[persist.sys.pwctl.gps.onlysave]: [0]
[persist.sys.pwctl.guru]: [1]
[persist.sys.pwctl.onlysave]: [1]
[persist.sys.pwctl.wl]: [1]
[persist.sys.sdcardfs]: [force_on]
[persist.sys.sf.boostpolicy]: [6]
[persist.sys.sf.color_saturation]: [1.0]
[persist.sys.special_datestr]: [W25.60.1]
[persist.sys.ss.enable]: [true]
[persist.sys.ss.habit]: [true]
[persist.sys.ss.hmm]: [true]
[persist.sys.ss.predict]: [false]
[persist.sys.ss.scene]: [true]
[persist.sys.ss.scroll]: [false]
[persist.sys.ss.sr.enable]: [true]
[persist.sys.ss.track]: [true]
[persist.sys.ss.uhc.enable]: [true]
[persist.sys.support.antenna]: [false]
[persist.sys.support.typeC]: [true]
[persist.sys.support.vram]: [true]
[persist.sys.support.vramselect]: [false]
[persist.sys.support.vt]: [true]
[persist.sys.thermal.hightempkiller]: [1]
[persist.sys.time.offset]: [28800000]
[persist.sys.timezone]: [Asia/Shanghai]
[persist.sys.unievent.enabled]: [1]
[persist.sys.unisoc_delayanimationfinish]: [true]
[persist.sys.unisoc_dyn_insert_frame]: [true]
[persist.sys.unisoc_game_boost]: [true]
[persist.sys.unisoc_smart_animation]: [true]
[persist.sys.usb.config]: [adb]
[persist.sys.vilte.socket]: [ap]
[persist.sys.vram_alter_enable]: [false]
[persist.sys.vramenable]: [true]
[persist.sys.vramsize]: [4096M]
[persist.sys.vramstoragelifetime]: [0]
[persist.sys.vramversion]: [3.0]
[persist.sys.wfc.supp_dual_sim]: [true]
[persist.sys.wifi.reset.devpath]: [devices/platform/87000000.cpwcn-btwf/87000000.cpwcn-btwf:sprd-wlan]
[persist.vendor.sys.isfirstboot]: [0]
[persist.vendor.sys.modem.diag]: [disable]
[persist.vendor.sys.modem.reboot]: [0xff]
[persist.vendor.sys.modem.save_dump]: [1]
[persist.vendor.sys.modemreset]: [1]
[persist.vendor.sys.single.imsstack]: [true]
[persist.vendor.sys.sp.save_dump]: [1]
[persist.vendor.sys.volte.enable]: [true]
[persist.vendor.sys.wcnreset]: [1]
[persist.vendor.sys.wcnstate]: [0]
[persist.wm.extensions.enabled]: [true]
[pm.dexopt.ab-ota]: [speed-profile]
[pm.dexopt.bg-dexopt]: [speed-profile]
[pm.dexopt.boot-after-mainline-update]: [verify]
[pm.dexopt.boot-after-ota]: [verify]
[pm.dexopt.cmdline]: [verify]
[pm.dexopt.first-boot]: [verify]
[pm.dexopt.inactive]: [verify]
[pm.dexopt.install]: [speed-profile]
[pm.dexopt.install-bulk]: [speed-profile]
[pm.dexopt.install-bulk-downgraded]: [verify]
[pm.dexopt.install-bulk-secondary]: [verify]
[pm.dexopt.install-bulk-secondary-downgraded]: [verify]
[pm.dexopt.install-fast]: [skip]
[pm.dexopt.post-boot]: [verify]
[pm.dexopt.shared]: [speed]
[remote_provisioning.enable_rkpd]: [true]
[ril.data.ps.reject]: [0,]
[ro.actionable_compatible_property.enabled]: [true]
[ro.adb.secure]: [1]
[ro.allow.mock.location]: [0]
[ro.apex.updatable]: [true]
[ro.appsflyer.preinstall.path]: [/system/etc/pre_install_tiktok.appsflyer]
[ro.audio.bigvolume.music_speaker]: [2]
[ro.audio.bigvolume.voice_earpiece]: [2]
[ro.audio.bigvolume.voice_speaker]: [2]
[ro.baseband]: [unknown]
[ro.bionic.2nd_arch]: [arm]
[ro.bionic.2nd_cpu_variant]: [cortex-a55]
[ro.bionic.arch]: [arm64]
[ro.bionic.cpu_variant]: [cortex-a75]
[ro.board.api_level]: [33]
[ro.board.first_api_level]: [33]
[ro.board.platform]: [ums9230]
[ro.boot.auto.chipid]: [UMS9230-AC]
[ro.boot.auto.efuse]: [UMS9230]
[ro.boot.avb_version]: [1.3]
[ro.boot.boot_devices]: [soc/soc:ap-apb/201d0000.sdio]
[ro.boot.carrier_group]: [OM]
[ro.boot.code]: [A601N]
[ro.boot.ddr_size]: [4096M]
[ro.boot.ddrsize]: [4096M]
[ro.boot.ddrsize.range]: [[4096,5120)]
[ro.boot.dpc]: [false]
[ro.boot.dswdten]: [enabled]
[ro.boot.dtbo_idx]: [11]
[ro.boot.dvfs_set]: [0x0,0,0]
[ro.boot.dynamic_partitions]: [true]
[ro.boot.ecid]: [23724000]
[ro.boot.fingerprint_support]: [0]
[ro.boot.flash.locked]: [1]
[ro.boot.force.user_adb]: [1]
[ro.boot.force_normal_boot]: [1]
[ro.boot.hardware]: [ums9230_6h10]
[ro.boot.lwfq.type]: [1]
[ro.boot.mode]: [normal]
[ro.boot.nfc_support]: [1]
[ro.boot.odm_customized_name]: [A601N]
[ro.boot.oem_tag]: [BR]
[ro.boot.payjoy]: [true]
[ro.boot.pcb_state]: [2]
[ro.boot.pmic.chipid]: [2730]
[ro.boot.product.hardware.sku]: [NFC_dualsim]
[ro.boot.project_name]: [A01]
[ro.boot.segment_efuse_sta]: [0]
[ro.boot.sim_count]: [2]
[ro.boot.slot_suffix]: [_a]
[ro.boot.tclsn]: [A601N22XGKI0193]
[ro.boot.tclsn2]: [00743A41060D90A8]
[ro.boot.tct.platform]: [SPRD]
[ro.boot.vbmeta.avb_version]: [1.1]
[ro.boot.vbmeta.device]: [PARTUUID=1.0]
[ro.boot.vbmeta.device_state]: [locked]
[ro.boot.vbmeta.digest]: [a504efa9f3e482b3d49f385109723b99a6450fffcf8053717fd024265a51fb73]
[ro.boot.vbmeta.hash_alg]: [sha256]
[ro.boot.vbmeta.size]: [50944]
[ro.boot.vendor.skip.init]: [0]
[ro.boot.verifiedbootstate]: [green]
[ro.boot.veritymode]: [enforcing]
[ro.boot.veritymode.managed]: [yes]
[ro.boot.wdten]: [e551]
[ro.bootimage.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.bootimage.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.bootloader]: [unknown]
[ro.bootmode]: [normal]
[ro.build.ab_update]: [true]
[ro.build.backported_fixes.alias_bitset.long_list]: [2]
[ro.build.characteristics]: [default]
[ro.build.date]: [Fri Aug  1 05:16:19 CST 2025]
[ro.build.date.utc]: [1753996579]
[ro.build.description]: [ums9230_6h10_Natv-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0101:user/release-keys]
[ro.build.flavor]: [ussi_arm64_full-user]
[ro.build.host]: [0056753jg]
[ro.build.id]: [AP3A.240905.015.A2]
[ro.build.product]: [ussi_arm64]
[ro.build.tags]: [release-keys]
[ro.build.type]: [user]
[ro.build.user]: [common1]
[ro.build.version.all_codenames]: [REL]
[ro.build.version.base_os]: []
[ro.build.version.codename]: [REL]
[ro.build.version.incremental]: [A0101]
[ro.build.version.known_codenames]: [Base,Base11,Cupcake,Donut,Eclair,Eclair01,EclairMr1,Froyo,Gingerbread,GingerbreadMr1,Honeycomb,HoneycombMr1,HoneycombMr2,IceCreamSandwich,IceCreamSandwichMr1,JellyBean,JellyBeanMr1,JellyBeanMr2,Kitkat,KitkatWatch,Lollipop,LollipopMr1,M,N,NMr1,O,OMr1,P,Q,R,S,Sv2,Tiramisu,UpsideDownCake,VanillaIceCream]
[ro.build.version.min_supported_target_sdk]: [28]
[ro.build.version.preview_sdk]: [0]
[ro.build.version.preview_sdk_fingerprint]: [REL]
[ro.build.version.release]: [15]
[ro.build.version.release_or_codename]: [15]
[ro.build.version.release_or_preview_display]: [15]
[ro.build.version.sdk]: [35]
[ro.build.version.security_patch]: [2025-06-05]
[ro.carrier]: [oversea]
[ro.com.android.dataroaming]: [false]
[ro.com.google.clientidbase]: [android-tcl]
[ro.com.google.clientidbase.ms]: [android-tcl-gep1]
[ro.com.google.clientidbase.vs]: [android-tcl-gep1]
[ro.com.google.gmsversion]: [15_202503]
[ro.com.google.lens.oem_camera_package]: [com.android.camera2]
[ro.com.google.lens.oem_image_package]: [com.google.android.apps.photos]
[ro.config.alarm_alert]: [Atmospheric_Forest-default.mp3]
[ro.config.alarm_vol_default]: [13]
[ro.config.alarm_vol_steps]: [15]
[ro.config.isolated_compilation_enabled]: [true]
[ro.config.media_vol_default]: [13]
[ro.config.media_vol_steps]: [15]
[ro.config.notification_sound]: [Paint.mp3]
[ro.config.ringtone]: [Bloom.mp3,Bloom.mp3]
[ro.config.system_vol_default]: [13]
[ro.config.system_vol_steps]: [15]
[ro.config.vc_call_vol_default]: [5]
[ro.config.vc_call_vol_steps]: [7]
[ro.crypto.metadata.enabled]: [true]
[ro.crypto.state]: [encrypted]
[ro.crypto.type]: [file]
[ro.dalvik.vm.enable_uffd_gc]: [true]
[ro.dalvik.vm.native.bridge]: [0]
[ro.debuggable]: [0]
[ro.ecid]: [23724000]
[ro.force.debuggable]: [0]
[ro.frp.pst]: [/dev/block/by-name/persist]
[ro.fuse.bpf.is_running]: [true]
[ro.hardware]: [ums9230_6h10]
[ro.hardware.audio.primary]: [ums9230]
[ro.hardware.camera]: [unisoc]
[ro.hardware.egl]: [mali]
[ro.hardware.hwcomposer]: [unisoc]
[ro.hardware.sensors]: [unisoc]
[ro.hw_timeout_multiplier]: [2]
[ro.hwui.use_vulkan]: [true]
[ro.kernel.version]: [5.15]
[ro.launcher.desktopgrid]: [true]
[ro.launcher.dynamic]: [false]
[ro.launcher.multimode]: [true]
[ro.launcher.notifbadge.count]: [true]
[ro.llndk.api_level]: [202404]
[ro.lmk.filecache_min_kb]: [153600]
[ro.lmk.kill_timeout_ms]: [200]
[ro.lmk.psi_complete_stall_ms]: [500]
[ro.lmk.stall_limit_critical]: [40]
[ro.lmk.swap_compression_ratio]: [0]
[ro.lmk.swap_free_low_percentage]: [20]
[ro.logd.size.stats]: [64K]
[ro.media.recoderEIS.enabled]: [true]
[ro.media.wfd.rgb.enabled]: [true]
[ro.odm.build.date]: [Fri Aug  1 05:16:19 CST 2025]
[ro.odm.build.date.utc]: [1753996579]
[ro.odm.build.description]: [ums9230_6h10_Natv-user 13 TP1A.220624.014 601NA0101 release-keys]
[ro.odm.build.display.id]: [ums9230_6h10_Natv-user 13 TP1A.220624.014 601NA0101 release-keys]
[ro.odm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0101:user/release-keys]
[ro.odm.build.version.incremental]: [A0101]
[ro.odm.def.ota.ver]: [601NA0101]
[ro.odm.tct.curef]: [A601N-2*LCBR12]
[ro.oem.key1]: [23724000]
[ro.opa.eligible_device]: [true]
[ro.opengles.version]: [196610]
[ro.postinstall.fstab.prefix]: [/product]
[ro.product.ab_ota_partitions]: [boot,dtbo,init_boot,l_agdsp,l_deltanv,l_fixnv1,l_fixnv2,l_gdsp,l_ldsp,l_modem,mmcblk0boot1,odm,pm_sys,product,sdc,sml,system,system_dlkm,system_ext,teecfg,trustos,uboot,vbmeta,vbmeta_odm,vbmeta_product,vbmeta_system,vbmeta_system_ext,vbmeta_vendor,vendor,vendor_boot,vendor_dlkm]
[ro.product.assistanttouch]: [false]
[ro.product.board]: [A601N]
[ro.product.brand]: [Alcatel]
[ro.product.build.date]: [Fri Aug  1 05:16:20 CST 2025]
[ro.product.build.date.utc]: [1753996580]
[ro.product.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.product.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.product.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0101:user/release-keys]
[ro.product.build.id]: [AP3A.240905.015.A2]
[ro.product.build.tags]: [release-keys]
[ro.product.build.type]: [user]
[ro.product.build.version.incremental]: [A0101]
[ro.product.build.version.release]: [15]
[ro.product.build.version.release_or_codename]: [15]
[ro.product.build.version.sdk]: [35]
[ro.product.cpu.abi]: [arm64-v8a]
[ro.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.product.cpu.abilist64]: [arm64-v8a]
[ro.product.cpu.pagesize.max]: [16384]
[ro.product.device]: [A01]
[ro.product.first_api_level]: [35]
[ro.product.locale]: [en-US]
[ro.product.manufacturer]: [TCL]
[ro.product.model]: [A601N]
[ro.product.name]: [A601N]
[ro.product.odm.brand]: [Alcatel]
[ro.product.odm.device]: [A01]
[ro.product.odm.manufacturer]: [TCL]
[ro.product.odm.model]: [A601N]
[ro.product.odm.name]: [A601N]
[ro.product.product.brand]: [Alcatel]
[ro.product.product.device]: [A01]
[ro.product.product.manufacturer]: [TCL]
[ro.product.product.model]: [A601N]
[ro.product.product.name]: [A601N]
[ro.product.publicname]: [Alcatel A62]
[ro.product.system.brand]: [Alcatel]
[ro.product.system.device]: [A01]
[ro.product.system.manufacturer]: [TCL]
[ro.product.system.model]: [A601N]
[ro.product.system.name]: [A601N]
[ro.product.system_dlkm.brand]: [Alcatel]
[ro.product.system_dlkm.device]: [A01]
[ro.product.system_dlkm.manufacturer]: [TCL]
[ro.product.system_dlkm.model]: [A601N]
[ro.product.system_dlkm.name]: [A601N]
[ro.product.system_ext.brand]: [Alcatel]
[ro.product.system_ext.device]: [A01]
[ro.product.system_ext.manufacturer]: [TCL]
[ro.product.system_ext.model]: [A601N]
[ro.product.system_ext.name]: [A601N]
[ro.product.tcl.dumysuffix]: [DMY]
[ro.product.vendor.brand]: [Alcatel]
[ro.product.vendor.device]: [A01]
[ro.product.vendor.manufacturer]: [TCL]
[ro.product.vendor.model]: [A601N]
[ro.product.vendor.name]: [A601N]
[ro.product.vendor.odm]: [true]
[ro.product.vendor_dlkm.brand]: [Alcatel]
[ro.product.vendor_dlkm.device]: [A01]
[ro.product.vendor_dlkm.manufacturer]: [TCL]
[ro.product.vendor_dlkm.model]: [A601N]
[ro.product.vendor_dlkm.name]: [A601N]
[ro.property_service.version]: [2]
[ro.revision]: [0]
[ro.secure]: [1]
[ro.secure_boot.state]: [1]
[ro.setupwizard.rotation_locked]: [true]
[ro.sf.lcd_density]: [260]
[ro.simlock.onekey.lock]: [0]
[ro.simlock.unlock.autoshow]: [1]
[ro.simlock.unlock.bynv]: [0]
[ro.soc.manufacturer]: [Spreadtrum]
[ro.soc.model]: [T606]
[ro.sprd.pwctl.ultra.message]: [1]
[ro.sprd.superresolution]: [1]
[ro.sr.displaysize.defaultresolution]: [0]
[ro.sr.displaysize.lowresolution]: [1]
[ro.sr.tp_screen_off]: [true]
[ro.support_one_handed_mode]: [true]
[ro.surface_flinger.force_hwc_copy_for_virtual_displays]: [true]
[ro.surface_flinger.game_default_frame_rate_override]: [60]
[ro.surface_flinger.has_HDR_display]: [false]
[ro.surface_flinger.has_wide_color_display]: [false]
[ro.surface_flinger.max_frame_buffer_acquired_buffers]: [3]
[ro.surface_flinger.max_virtual_display_dimension]: [4096]
[ro.surface_flinger.present_time_offset_from_vsync_ns]: [0]
[ro.surface_flinger.primary_display_orientation]: [ORIENTATION_0]
[ro.surface_flinger.protected_contents]: [true]
[ro.surface_flinger.running_without_sync_framework]: [false]
[ro.surface_flinger.set_display_power_timer_ms]: [1000]
[ro.surface_flinger.set_idle_timer_ms]: [4000]
[ro.surface_flinger.set_touch_timer_ms]: [200]
[ro.surface_flinger.start_graphics_allocator_service]: [false]
[ro.surface_flinger.use_content_detection_for_refresh_rate]: [true]
[ro.surface_flinger.use_context_priority]: [true]
[ro.surface_flinger.use_vr_flinger]: [false]
[ro.surface_flinger.vsync_event_phase_offset_ns]: [1000000]
[ro.surface_flinger.vsync_sf_event_phase_offset_ns]: [1000000]
[ro.sys.pwctl.ultrasaving]: [1]
[ro.system.build.date]: [Fri Aug  1 05:16:19 CST 2025]
[ro.system.build.date.utc]: [1753996579]
[ro.system.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0101:user/release-keys]
[ro.system.build.id]: [AP3A.240905.015.A2]
[ro.system.build.tags]: [release-keys]
[ro.system.build.type]: [user]
[ro.system.build.version.incremental]: [A0101]
[ro.system.build.version.release]: [15]
[ro.system.build.version.release_or_codename]: [15]
[ro.system.build.version.sdk]: [35]
[ro.system.component.label]: [SYSTEM-Android15--U1.0-W25.30.5]
[ro.system.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist64]: [arm64-v8a]
[ro.system_dlkm.build.date]: [Fri Aug  1 05:16:20 CST 2025]
[ro.system_dlkm.build.date.utc]: [1753996580]
[ro.system_dlkm.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system_dlkm.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system_dlkm.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0101:user/release-keys]
[ro.system_dlkm.build.id]: [AP3A.240905.015.A2]
[ro.system_dlkm.build.tags]: [release-keys]
[ro.system_dlkm.build.type]: [user]
[ro.system_dlkm.build.version.incremental]: [A0101]
[ro.system_dlkm.build.version.release]: [15]
[ro.system_dlkm.build.version.release_or_codename]: [15]
[ro.system_dlkm.build.version.sdk]: [35]
[ro.system_ext.build.date]: [Fri Aug  1 05:16:19 CST 2025]
[ro.system_ext.build.date.utc]: [1753996579]
[ro.system_ext.build.description]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system_ext.build.display.id]: [ussi_arm_go-user 15 AP3A.240905.015.A2 601NA0101 release-keys]
[ro.system_ext.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0101:user/release-keys]
[ro.system_ext.build.id]: [AP3A.240905.015.A2]
[ro.system_ext.build.tags]: [release-keys]
[ro.system_ext.build.type]: [user]
[ro.system_ext.build.version.incremental]: [A0101]
[ro.system_ext.build.version.release]: [15]
[ro.system_ext.build.version.release_or_codename]: [15]
[ro.system_ext.build.version.sdk]: [35]
[ro.tct.curef]: [A601N-2*LCBR12]
[ro.tct.trace.bsn]: [A601N22XGKI0193]
[ro.telephony.default_network]: [9]
[ro.treble.enabled]: [true]
[ro.unipnp.switch]: [true]
[ro.vendor.api_level]: [33]
[ro.vendor.arm.egl.configs.nv12.hal_format]: [0x100]
[ro.vendor.arm.egl.configs.nv12.recordable]: [true]
[ro.vendor.arm.egl.configs.nv16.hal_format]: [0x10]
[ro.vendor.arm.egl.configs.nv16.recordable]: [true]
[ro.vendor.arm.egl.configs.nv21.hal_format]: [0x101]
[ro.vendor.arm.egl.configs.nv21.recordable]: [true]
[ro.vendor.arm.egl.configs.p010.hal_format]: [0x104]
[ro.vendor.arm.egl.configs.p010.recordable]: [true]
[ro.vendor.arm.egl.configs.p210.hal_format]: [0x105]
[ro.vendor.arm.egl.configs.p210.recordable]: [true]
[ro.vendor.arm.egl.configs.q410.hal_format]: [0x10a]
[ro.vendor.arm.egl.configs.q410.recordable]: [true]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.hal_format]: [0x2b]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.hal_format]: [0x16]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.recordable]: [false]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.hal_format]: [0x4]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.hal_format]: [0x0]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.hal_format]: [0x23]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.hal_format]: [0x2]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.hal_format]: [0x1]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.recordable]: [true]
[ro.vendor.arm.egl.configs.y0l2.hal_format]: [0x103]
[ro.vendor.arm.egl.configs.y0l2.recordable]: [true]
[ro.vendor.arm.egl.configs.y210.hal_format]: [0x106]
[ro.vendor.arm.egl.configs.y210.recordable]: [true]
[ro.vendor.arm.egl.configs.y410.hal_format]: [0x107]
[ro.vendor.arm.egl.configs.y410.recordable]: [true]
[ro.vendor.arm.egl.configs.yuv420.hal_format]: [0x108]
[ro.vendor.arm.egl.configs.yuv420.recordable]: [true]
[ro.vendor.arm.egl.configs.yuyv.hal_format]: [0x102]
[ro.vendor.arm.egl.configs.yuyv.recordable]: [true]
[ro.vendor.arm.egl.configs.yvu420.hal_format]: [0x10e]
[ro.vendor.arm.egl.configs.yvu420.recordable]: [true]
[ro.vendor.arm.gralloc.afrc_chroma_usage_flags]: [0x100000000000000,0x80000000000000,0x180000000000000]
[ro.vendor.arm.gralloc.afrc_chroma_usage_mask]: [0x180000000000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_flags]: [0x40000000000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_mask]: [0x40000000000000]
[ro.vendor.arm.gralloc.no_afbc_usage_flags]: [0x20000000]
[ro.vendor.arm.gralloc.no_afbc_usage_mask]: [0x60000000]
[ro.vendor.arm.gralloc.shared_access_usage_flags]: [0x10000000]
[ro.vendor.arm.gralloc.shared_access_usage_mask]: [0x50000000]
[ro.vendor.build.date]: [Fri Aug  1 05:16:15 CST 2025]
[ro.vendor.build.date.utc]: [1753996575]
[ro.vendor.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0101:user/release-keys]
[ro.vendor.build.id]: [TP1A.220624.014]
[ro.vendor.build.tags]: [release-keys]
[ro.vendor.build.type]: [user]
[ro.vendor.build.version.incremental]: [A0101]
[ro.vendor.build.version.release]: [13]
[ro.vendor.build.version.release_or_codename]: [13]
[ro.vendor.build.version.sdk]: [33]
[ro.vendor.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist64]: [arm64-v8a]
[ro.vendor.version.release]: [601NA0101]
[ro.vendor_dlkm.build.date]: [Fri Aug  1 05:16:19 CST 2025]
[ro.vendor_dlkm.build.date.utc]: [1753996579]
[ro.vendor_dlkm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0101:user/release-keys]
[ro.vendor_dlkm.build.id]: [TP1A.220624.014]
[ro.vendor_dlkm.build.tags]: [release-keys]
[ro.vendor_dlkm.build.type]: [user]
[ro.vendor_dlkm.build.version.incremental]: [A0101]
[ro.vendor_dlkm.build.version.release]: [13]
[ro.vendor_dlkm.build.version.release_or_codename]: [13]
[ro.vendor_dlkm.build.version.sdk]: [33]
[ro.vndk.version]: [33]
[ro.wifi.channels]: []
[ro.zygote]: [zygote64_32]
[security.perf_harden]: [1]
[selinux.restorecon_recursive]: [/data/misc_ce/0]
[service.sf.present_timestamp]: [1]
[service.wait_for_bootanim]: [1]
[servicemanager.ready]: [true]
[setupwizard.theme]: [glif_v3_light]
[sys.boot.reason]: [reboot,userrequested]
[sys.boot_completed]: [1]
[sys.bootstat.first_boot_completed]: [1]
[sys.debug.fwc]: [0]
[sys.debug.monkey]: [0]
[sys.fota.boot.up]: [false]
[sys.fuse.transcode_enabled]: [true]
[sys.internal.emulated]: [1]
[sys.lmk.minfree_levels]: [18432:0,23040:100,27648:200,32256:250,55296:900,80640:950]
[sys.lmk.reportkills]: [1]
[sys.log.bootimes]: [1]
[sys.log.fwc]: []
[sys.log.wbootimes]: [1]
[sys.nhmonitor.bootmode.allow]: [true]
[sys.oem_unlock_allowed]: [0]
[sys.sysctl.extra_free_kbytes]: [13500]
[sys.system_server.start_count]: [1]
[sys.system_server.start_elapsed]: [11305]
[sys.system_server.start_uptime]: [11305]
[sys.usb.config]: [adb]
[sys.usb.configfs]: [1]
[sys.usb.controller]: [musb-hdrc.1.auto]
[sys.usb.mode]: [normal]
[sys.usb.mtp.disconnected]: [1754018627]
[sys.usb.state]: [adb]
[sys.use_memfd]: [false]
[sys.user.0.ce_available]: [true]
[sys.wifitracing.started]: [1]
[sys.ylog.bootimes]: [1]
[sys.ylog.file]: [/data/ylog/ap/000-0801_120153_poweron.ylog]
[sys.ylog.fwc]: [0]
[sys.ylog.path]: [/data/ylog/ap/]
[sys.ylog.version]: [5.0.0]
[sys.ylog.wbootimes]: [1]
[vendor.minidump.crash_reason]: [Normal]
[vendor.sys.ril.agps.active]: [0]
[vold.emulated.volume.ready]: [1]
[vold.has_adoptable]: [1]
[vold.has_compress]: [0]
[vold.has_quota]: [1]
[vold.has_reserved]: [1]


ylogctl q
Fri Aug  1 12:01:54 CST 2025
phoneinfo end
poweron start
Fri Aug  1 12:02:54 CST 2025


uptime
Fri Aug  1 12:02:54 CST 2025
 12:02:54 up 39 min,  0 users,  load average: 15.88, 15.15, 10.48


the log file is 
Fri Aug  1 12:02:54 CST 2025


getprop>/data/ylog/phone.info
Fri Aug  1 12:02:54 CST 2025


chmod 0777 /data/ylog/phone.info
Fri Aug  1 12:02:54 CST 2025
